import { defineConfig } from 'vitest/config';

/**
 * Coverage configuration for Appointment Planner Backend
 * Minimum coverage threshold: 70%
 */
export default defineConfig({
  test: {
    coverage: {
      // Include all source files for coverage analysis
      all: true,

      // Files to exclude from coverage
      exclude: [
        'node_modules/**',
        'dist/**',
        'docs/**',
        'src/tests/**',
        'src/**/*.test.ts',
        'src/**/*.spec.ts',
        'src/**/*.d.ts',
        'src/test-*.ts',
        'prisma/**',
        '**/*.config.{ts,js}',
        '**/coverage/**',
        // Exclude generated files
        'src/generated/**',
        // Exclude main entry point (usually just bootstrapping)
        'src/index.ts',
        // Exclude type-only files
        'src/types/**',
      ],

      // Files to include in coverage
      include: ['src/**/*.{ts,js}'],

      // Coverage provider
      provider: 'v8',

      // Coverage reporters
      reporter: [
        'text', // Console output
        'json', // JSON report for CI
        'html', // HTML report for local viewing
        'lcov', // LCOV format for external tools
        'text-summary', // Summary in console
      ],

      // Coverage thresholds - minimum 70%
      thresholds: {
        branches: 70, // Branch coverage
        functions: 70, // Function coverage
        lines: 70, // Line coverage
        statements: 70, // Statement coverage
      },

      // Report directory
      reportsDirectory: './coverage',

      // Clean coverage directory before running
      clean: true,

      // Skip coverage for files with no tests
      skipFull: false,
    },
  },
});
