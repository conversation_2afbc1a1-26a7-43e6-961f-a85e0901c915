// Using mock repository for demo
import type {
  Appointment,
  CreateAppointmentRequest,
  MockAppointmentRepository,
} from '../infrastructure/repositories/mockAppointmentRepository';

// Legacy imports (keeping for compatibility)
// import type {
//   Appointment,
//   CreateAppointmentRequest,
// } from '@beauty-crm/platform-appointment-unified';
// import { createAppointmentDomain } from '@beauty-crm/platform-appointment-unified';

import type { EmailService } from '../../infrastructure/services/emailService';
import type { OutboxRelayer } from '../../infrastructure/events/OutboxRelayer';
import { PrismaClient } from '@prisma/client';

export class AppointmentService {
  constructor(
    private appointmentRepository: MockAppointmentRepository,
    private emailService: EmailService,
    private outboxRelayer: OutboxRelayer
  ) {}

  async createAppointment(
    appointmentData: CreateAppointmentRequest
  ): Promise<Appointment> {
    // Create appointment directly using the repository
    const newAppointment = await this.appointmentRepository.create(
      appointmentData
    );

    // Send confirmation email
    if (newAppointment.customerEmail) {
      await this.emailService.sendAppointmentConfirmation(
        newAppointment.customerEmail,
        newAppointment
      );
    }

    // TODO: Add event to outbox
    // await this.outboxRelayer.add( ... );

    return newAppointment;
  }
}
