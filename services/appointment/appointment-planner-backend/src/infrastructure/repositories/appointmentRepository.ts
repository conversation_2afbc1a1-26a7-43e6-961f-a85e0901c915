/**
 * 🎯 APPOINTMENT REPOSITORY - Using Unified Library Types
 *
 * Local copy of SimpleAppointmentRepository to avoid import issues
 */

// Define the types locally to avoid validation issues
export interface Appointment {
  id: string;
  salonId: string;
  customerId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  treatmentId?: string;
  treatmentName: string;
  treatmentDuration?: number;
  treatmentPrice?: number;
  staffId?: string;
  startTime: Date;
  endTime: Date;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateAppointmentRequest {
  salonId: string;
  customerId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  treatmentId?: string;
  treatmentName: string;
  treatmentDuration?: number;
  treatmentPrice?: number;
  staffId?: string;
  startTime: Date | string;
  endTime: Date | string;
  notes?: string;
  status?: string;
}

// Generic type for any Prisma client that has an appointment model
type PrismaClientWithAppointment = {
  appointment: {
    findUnique: (args: any) => Promise<any>;
    create: (args: any) => Promise<any>;
    update: (args: any) => Promise<any>;
    delete: (args: any) => Promise<any>;
    findMany: (args: any) => Promise<any[]>;
  };
};

/**
 * Simple appointment repository with basic CRUD operations
 * Based on the unified library implementation
 */
export class AppointmentRepository {
  constructor(private prisma: PrismaClientWithAppointment) {}

  async findById(id: string): Promise<Appointment | null> {
    const prismaAppointment = await this.prisma.appointment.findUnique({
      where: { id },
    });

    if (!prismaAppointment) return null;

    return this.mapPrismaToAppointment(prismaAppointment);
  }

  async create(
    appointmentData: CreateAppointmentRequest,
  ): Promise<Appointment> {
    console.log('🎯 Creating appointment:', {
      customer: appointmentData.customerName,
      treatment: appointmentData.treatmentName,
      time: appointmentData.startTime,
    });

    const created = await this.prisma.appointment.create({
      data: {
        ...appointmentData,
        status: appointmentData.status || 'PENDING',
        startTime:
          typeof appointmentData.startTime === 'string'
            ? new Date(appointmentData.startTime)
            : appointmentData.startTime,
        endTime:
          typeof appointmentData.endTime === 'string'
            ? new Date(appointmentData.endTime)
            : appointmentData.endTime,
      },
    });

    console.log('✅ Appointment created successfully:', created.id);
    return this.mapPrismaToAppointment(created);
  }

  private mapPrismaToAppointment(prismaAppointment: any): Appointment {
    return {
      id: prismaAppointment.id,
      salonId: prismaAppointment.salonId,
      customerId: prismaAppointment.customerId,
      customerName: prismaAppointment.customerName,
      customerEmail: prismaAppointment.customerEmail,
      customerPhone: prismaAppointment.customerPhone,
      treatmentId: prismaAppointment.treatmentId,
      treatmentName: prismaAppointment.treatmentName,
      treatmentDuration: prismaAppointment.treatmentDuration,
      treatmentPrice: prismaAppointment.treatmentPrice,
      staffId: prismaAppointment.staffId,
      startTime: new Date(prismaAppointment.startTime),
      endTime: new Date(prismaAppointment.endTime),
      status: prismaAppointment.status,
      notes: prismaAppointment.notes,
      createdAt: new Date(prismaAppointment.createdAt),
      updatedAt: new Date(prismaAppointment.updatedAt),
    };
  }
}
