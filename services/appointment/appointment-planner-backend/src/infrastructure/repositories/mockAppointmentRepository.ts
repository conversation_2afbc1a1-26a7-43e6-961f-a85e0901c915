/**
 * 🎯 MOCK APPOINTMENT REPOSITORY - For Demo Purposes
 * 
 * Simple in-memory repository that works without external dependencies
 */

export interface CreateAppointmentRequest {
  salonId: string;
  customerId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  treatmentId?: string;
  treatmentName: string;
  treatmentDuration?: number;
  treatmentPrice?: number;
  staffId?: string;
  startTime: Date | string;
  endTime: Date | string;
  notes?: string;
}

export interface Appointment {
  id: string;
  salonId: string;
  customerId?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  treatmentId?: string;
  treatmentName: string;
  treatmentDuration?: number;
  treatmentPrice?: number;
  staffId?: string;
  startTime: Date;
  endTime: Date;
  status: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export class MockAppointmentRepository {
  private appointments: Appointment[] = [];
  private nextId = 1;

  async create(appointmentData: CreateAppointmentRequest): Promise<Appointment> {
    const appointment: Appointment = {
      id: `appointment-${this.nextId++}`,
      salonId: appointmentData.salonId,
      customerId: appointmentData.customerId || `customer-${Date.now()}`,
      customerName: appointmentData.customerName,
      customerEmail: appointmentData.customerEmail,
      customerPhone: appointmentData.customerPhone,
      treatmentId: appointmentData.treatmentId || `treatment-${Date.now()}`,
      treatmentName: appointmentData.treatmentName,
      treatmentDuration: appointmentData.treatmentDuration || 30,
      treatmentPrice: appointmentData.treatmentPrice || 50.00,
      staffId: appointmentData.staffId || `staff-${Date.now()}`,
      startTime: typeof appointmentData.startTime === 'string' 
        ? new Date(appointmentData.startTime) 
        : appointmentData.startTime,
      endTime: typeof appointmentData.endTime === 'string' 
        ? new Date(appointmentData.endTime) 
        : appointmentData.endTime,
      status: 'PENDING',
      notes: appointmentData.notes,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    this.appointments.push(appointment);
    
    console.log('✅ Mock appointment created:', {
      id: appointment.id,
      customer: appointment.customerName,
      treatment: appointment.treatmentName,
      time: appointment.startTime.toISOString(),
    });

    return appointment;
  }

  async findById(id: string): Promise<Appointment | null> {
    return this.appointments.find(apt => apt.id === id) || null;
  }

  async findAll(): Promise<Appointment[]> {
    return [...this.appointments];
  }

  async findBySalonId(salonId: string): Promise<Appointment[]> {
    return this.appointments.filter(apt => apt.salonId === salonId);
  }

  // Generate mock available time slots
  async findAvailableSlots(
    salonId: string,
    date: Date,
    duration: number = 30
  ): Promise<{ start: Date; end: Date }[]> {
    const slots = [];
    const startHour = 9; // 9 AM
    const endHour = 17; // 5 PM
    
    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += duration) {
        const start = new Date(date);
        start.setHours(hour, minute, 0, 0);
        
        const end = new Date(start);
        end.setMinutes(end.getMinutes() + duration);
        
        // Don't go past end hour
        if (end.getHours() >= endHour) break;
        
        slots.push({ start, end });
      }
    }
    
    console.log(`📅 Generated ${slots.length} available slots for ${salonId} on ${date.toDateString()}`);
    return slots;
  }

  // Get current appointments (for debugging)
  getAll(): Appointment[] {
    return [...this.appointments];
  }

  // Clear all appointments (for testing)
  clear(): void {
    this.appointments = [];
    this.nextId = 1;
    console.log('🧹 Cleared all mock appointments');
  }
}
