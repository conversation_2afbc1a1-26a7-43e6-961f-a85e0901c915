import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import { app } from '../../index';

describe('Appointment Planner API v1 Tests', () => {
  let prisma: PrismaClient;

  beforeAll(async () => {
    // Initialize test database
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.TEST_DATABASE_URL || 'file:./test.db',
        },
      },
    });

    // Run migrations for test database
    // Note: In a real setup, you'd want to use a separate test database
  });

  beforeEach(async () => {
    // Clean up database before each test
    await prisma.appointment.deleteMany();
    await prisma.appointmentOutbox.deleteMany();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('GET /api/v1/appointments/available-slots', () => {
    it('should return available time slots for a salon', async () => {
      const response = await request(app.fetch)
        .get('/api/v1/appointments/available-slots')
        .query({
          salonId: 'salon-123',
          date: '2024-01-15',
          treatmentDuration: 30,
        })
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should return 400 for missing required parameters', async () => {
      const response = await request(app.fetch)
        .get('/api/v1/appointments/available-slots')
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    it('should handle invalid date format', async () => {
      const response = await request(app.fetch)
        .get('/api/v1/appointments/available-slots')
        .query({
          salonId: 'salon-123',
          date: 'invalid-date',
          treatmentDuration: 30,
        })
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/appointments', () => {
    const validAppointmentData = {
      salonId: 'salon-123',
      salonName: 'Test Salon',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+1234567890',
      treatmentId: 'treatment-123',
      treatmentName: 'Haircut',
      treatmentDuration: 30,
      treatmentPrice: 35.0,
      staffId: 'staff-123',
      startTime: new Date('2024-01-15T10:00:00Z').toISOString(),
      endTime: new Date('2024-01-15T10:30:00Z').toISOString(),
      notes: 'First time customer',
    };

    it('should create a new appointment successfully', async () => {
      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .send(validAppointmentData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.customerName).toBe('John Doe');
      expect(response.body.data.customerEmail).toBe('<EMAIL>');
    });

    it('should return 400 for invalid appointment data', async () => {
      const invalidData = {
        ...validAppointmentData,
        customerEmail: 'invalid-email', // Invalid email format
      };

      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .send(invalidData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBeDefined();
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteData = {
        customerName: 'John Doe',
        // Missing required fields
      };

      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .send(incompleteData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.details).toBeDefined();
    });

    it('should handle conflicting appointment times', async () => {
      // First, create an appointment
      await request(app.fetch)
        .post('/api/v1/appointments')
        .send(validAppointmentData)
        .expect(201);

      // Try to create another appointment at the same time
      const conflictingData = {
        ...validAppointmentData,
        customerName: 'Jane Doe',
        customerEmail: '<EMAIL>',
      };

      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .send(conflictingData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('conflict');
    });
  });

  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const response = await request(app.fetch).get('/health').expect(200);

      expect(response.body.status).toBe('healthy');
      expect(response.body.service).toBe('appointment-planner-backend');
    });
  });
});
