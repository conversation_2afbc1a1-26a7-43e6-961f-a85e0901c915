import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import { Hono } from 'hono';
import { PrismaClient } from '@prisma/client';

// Create a test app instance
const createTestApp = () => {
  const app = new Hono();

  // Health check endpoint
  app.get('/health', (c) => {
    return c.json({
      status: 'healthy',
      service: 'appointment-planner-backend',
      timestamp: new Date().toISOString(),
    });
  });

  // Mock available slots endpoint
  app.get('/api/v1/appointments/available-slots', (c) => {
    const salonId = c.req.query('salonId');
    const date = c.req.query('date');
    const treatmentDuration = c.req.query('treatmentDuration');

    if (!salonId || !date) {
      return c.json(
        {
          success: false,
          error: 'Missing required parameters: salonId and date',
        },
        400,
      );
    }

    // Mock available slots data
    const slots = [
      {
        startTime: `${date}T09:00:00Z`,
        endTime: `${date}T09:30:00Z`,
        available: true,
      },
      {
        startTime: `${date}T09:30:00Z`,
        endTime: `${date}T10:00:00Z`,
        available: true,
      },
      {
        startTime: `${date}T10:00:00Z`,
        endTime: `${date}T10:30:00Z`,
        available: false,
      },
      {
        startTime: `${date}T10:30:00Z`,
        endTime: `${date}T11:00:00Z`,
        available: true,
      },
    ];

    return c.json({
      success: true,
      data: slots,
      meta: {
        salonId,
        date,
        treatmentDuration: Number(treatmentDuration) || 30,
      },
    });
  });

  // Mock create appointment endpoint
  app.post('/api/v1/appointments', async (c) => {
    try {
      const body = await c.req.json();

      // Basic validation
      const requiredFields = [
        'salonId',
        'customerName',
        'customerEmail',
        'treatmentName',
        'startTime',
        'endTime',
      ];

      const missingFields = requiredFields.filter((field) => !body[field]);

      if (missingFields.length > 0) {
        return c.json(
          {
            success: false,
            error: 'Missing required fields',
            details: missingFields.map((field) => ({
              path: [field],
              message: `${field} is required`,
            })),
          },
          400,
        );
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(body.customerEmail)) {
        return c.json(
          {
            success: false,
            error: 'Invalid email format',
            details: [
              {
                path: ['customerEmail'],
                message: 'Please provide a valid email address',
              },
            ],
          },
          400,
        );
      }

      // Mock successful creation
      const appointment = {
        id: `appointment-${Date.now()}`,
        ...body,
        status: 'PENDING',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return c.json(
        {
          success: true,
          data: appointment,
        },
        201,
      );
    } catch (error) {
      return c.json(
        {
          success: false,
          error: 'Invalid JSON payload',
        },
        400,
      );
    }
  });

  return app;
};

describe('Supertest Integration Tests - Appointment Planner API v1', () => {
  let app: Hono;

  beforeAll(() => {
    app = createTestApp();
  });

  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const response = await request(app.fetch).get('/health').expect(200);

      expect(response.body).toMatchObject({
        status: 'healthy',
        service: 'appointment-planner-backend',
      });
      expect(response.body.timestamp).toBeDefined();
    });
  });

  describe('GET /api/v1/appointments/available-slots', () => {
    it('should return available slots with valid parameters', async () => {
      const response = await request(app.fetch)
        .get('/api/v1/appointments/available-slots')
        .query({
          salonId: 'salon-123',
          date: '2024-01-15',
          treatmentDuration: '30',
        })
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.any(Array),
        meta: {
          salonId: 'salon-123',
          date: '2024-01-15',
          treatmentDuration: 30,
        },
      });

      expect(response.body.data).toHaveLength(4);
      expect(response.body.data[0]).toMatchObject({
        startTime: expect.stringMatching(
          /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/,
        ),
        endTime: expect.stringMatching(
          /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/,
        ),
        available: expect.any(Boolean),
      });
    });

    it('should return 400 when salonId is missing', async () => {
      const response = await request(app.fetch)
        .get('/api/v1/appointments/available-slots')
        .query({
          date: '2024-01-15',
        })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('Missing required parameters'),
      });
    });

    it('should return 400 when date is missing', async () => {
      const response = await request(app.fetch)
        .get('/api/v1/appointments/available-slots')
        .query({
          salonId: 'salon-123',
        })
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: expect.stringContaining('Missing required parameters'),
      });
    });

    it('should work without treatmentDuration (optional parameter)', async () => {
      const response = await request(app.fetch)
        .get('/api/v1/appointments/available-slots')
        .query({
          salonId: 'salon-123',
          date: '2024-01-15',
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.meta.treatmentDuration).toBe(30); // default value
    });
  });

  describe('POST /api/v1/appointments', () => {
    const validAppointmentData = {
      salonId: 'salon-123',
      salonName: 'Test Salon',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+1234567890',
      treatmentId: 'treatment-123',
      treatmentName: 'Haircut',
      treatmentDuration: 30,
      treatmentPrice: 35.0,
      staffId: 'staff-123',
      startTime: '2024-01-15T10:00:00Z',
      endTime: '2024-01-15T10:30:00Z',
      notes: 'First time customer',
    };

    it('should create appointment with valid data', async () => {
      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .send(validAppointmentData)
        .expect(201);

      expect(response.body).toMatchObject({
        success: true,
        data: {
          id: expect.stringMatching(/^appointment-\d+$/),
          ...validAppointmentData,
          status: 'PENDING',
          createdAt: expect.stringMatching(
            /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
          ),
          updatedAt: expect.stringMatching(
            /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/,
          ),
        },
      });
    });

    it('should return 400 for missing required fields', async () => {
      const incompleteData = {
        customerName: 'John Doe',
        // Missing other required fields
      };

      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .send(incompleteData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'Missing required fields',
        details: expect.any(Array),
      });

      expect(response.body.details.length).toBeGreaterThan(0);
    });

    it('should return 400 for invalid email format', async () => {
      const invalidData = {
        ...validAppointmentData,
        customerEmail: 'invalid-email-format',
      };

      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'Invalid email format',
        details: [
          {
            path: ['customerEmail'],
            message: 'Please provide a valid email address',
          },
        ],
      });
    });

    it('should return 400 for invalid JSON', async () => {
      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'Invalid JSON payload',
      });
    });
  });

  describe('Content-Type Headers', () => {
    it('should handle JSON content type correctly', async () => {
      const validData = {
        salonId: 'salon-123',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
      };

      const response = await request(app.fetch)
        .post('/api/v1/appointments')
        .set('Content-Type', 'application/json')
        .send(validData)
        .expect(201);

      expect(response.body.success).toBe(true);
    });
  });
});
