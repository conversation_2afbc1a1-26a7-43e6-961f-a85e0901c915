import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AppointmentRepository } from '../../infrastructure/repositories/appointmentRepository';
import type { PrismaClient } from '@prisma/client';
import type { Appointment } from '@beauty-crm/platform-appointment-unified';

describe('AppointmentRepository', () => {
  let appointmentRepository: AppointmentRepository;
  let mockPrisma: PrismaClient;

  const mockAppointmentData = {
    id: 'appointment-123',
    salonId: 'salon-123',
    salonName: 'Test Salon',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    treatmentId: 'treatment-123',
    treatmentName: 'Haircut',
    treatmentDuration: 30,
    treatmentPrice: 35.00,
    staffId: 'staff-123',
    startTime: new Date('2024-01-15T10:00:00Z'),
    endTime: new Date('2024-01-15T10:30:00Z'),
    status: 'PENDING',
    notes: 'First time customer',
    createdAt: new Date('2024-01-15T08:00:00Z'),
    updatedAt: new Date('2024-01-15T08:00:00Z'),
  };

  const mockAppointment: Appointment = {
    ...mockAppointmentData,
  };

  beforeEach(() => {
    // Create mock Prisma client
    mockPrisma = {
      appointment: {
        create: vi.fn(),
        findUnique: vi.fn(),
        findMany: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
      },
    } as unknown as PrismaClient;

    appointmentRepository = new AppointmentRepository(mockPrisma);
  });

  describe('create', () => {
    it('should create appointment successfully', async () => {
      // Arrange
      vi.mocked(mockPrisma.appointment.create).mockResolvedValue(mockAppointmentData);

      // Act
      const result = await appointmentRepository.create(mockAppointment);

      // Assert
      expect(result).toEqual(mockAppointment);
      expect(mockPrisma.appointment.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          salonId: 'salon-123',
          customerName: 'John Doe',
          customerEmail: '<EMAIL>',
          treatmentName: 'Haircut',
        }),
      });
    });

    it('should handle database errors during creation', async () => {
      // Arrange
      const dbError = new Error('Database constraint violation');
      vi.mocked(mockPrisma.appointment.create).mockRejectedValue(dbError);

      // Act & Assert
      await expect(appointmentRepository.create(mockAppointment))
        .rejects.toThrow('Database constraint violation');
    });

    it('should create appointment with minimal data', async () => {
      // Arrange
      const minimalAppointment: Appointment = {
        id: 'appointment-456',
        salonId: 'salon-123',
        salonName: 'Test Salon',
        customerName: 'Jane Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Basic Cut',
        treatmentDuration: 30,
        treatmentPrice: 25.00,
        startTime: new Date('2024-01-15T14:00:00Z'),
        endTime: new Date('2024-01-15T14:30:00Z'),
        status: 'PENDING',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const minimalData = {
        ...minimalAppointment,
        customerPhone: null,
        staffId: null,
        treatmentId: null,
        notes: null,
      };

      vi.mocked(mockPrisma.appointment.create).mockResolvedValue(minimalData);

      // Act
      const result = await appointmentRepository.create(minimalAppointment);

      // Assert
      expect(result.customerName).toBe('Jane Doe');
      expect(result.customerPhone).toBeUndefined();
      expect(mockPrisma.appointment.create).toHaveBeenCalledOnce();
    });
  });

  describe('findById', () => {
    it('should find appointment by id successfully', async () => {
      // Arrange
      vi.mocked(mockPrisma.appointment.findUnique).mockResolvedValue(mockAppointmentData);

      // Act
      const result = await appointmentRepository.findById('appointment-123');

      // Assert
      expect(result).toEqual(mockAppointment);
      expect(mockPrisma.appointment.findUnique).toHaveBeenCalledWith({
        where: { id: 'appointment-123' },
      });
    });

    it('should return null when appointment not found', async () => {
      // Arrange
      vi.mocked(mockPrisma.appointment.findUnique).mockResolvedValue(null);

      // Act
      const result = await appointmentRepository.findById('non-existent-id');

      // Assert
      expect(result).toBeNull();
      expect(mockPrisma.appointment.findUnique).toHaveBeenCalledWith({
        where: { id: 'non-existent-id' },
      });
    });

    it('should handle database errors during find', async () => {
      // Arrange
      const dbError = new Error('Database connection lost');
      vi.mocked(mockPrisma.appointment.findUnique).mockRejectedValue(dbError);

      // Act & Assert
      await expect(appointmentRepository.findById('appointment-123'))
        .rejects.toThrow('Database connection lost');
    });
  });

  describe('findByEmail', () => {
    it('should find appointments by email successfully', async () => {
      // Arrange
      const appointments = [mockAppointmentData];
      vi.mocked(mockPrisma.appointment.findMany).mockResolvedValue(appointments);

      // Act
      const result = await appointmentRepository.findByEmail('<EMAIL>');

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0]).toEqual(mockAppointment);
      expect(mockPrisma.appointment.findMany).toHaveBeenCalledWith({
        where: { customerEmail: '<EMAIL>' },
        orderBy: { startTime: 'desc' },
      });
    });

    it('should return empty array when no appointments found', async () => {
      // Arrange
      vi.mocked(mockPrisma.appointment.findMany).mockResolvedValue([]);

      // Act
      const result = await appointmentRepository.findByEmail('<EMAIL>');

      // Assert
      expect(result).toEqual([]);
      expect(mockPrisma.appointment.findMany).toHaveBeenCalledWith({
        where: { customerEmail: '<EMAIL>' },
        orderBy: { startTime: 'desc' },
      });
    });

    it('should handle multiple appointments for same email', async () => {
      // Arrange
      const appointment1 = { ...mockAppointmentData, id: 'appointment-1' };
      const appointment2 = { ...mockAppointmentData, id: 'appointment-2' };
      vi.mocked(mockPrisma.appointment.findMany).mockResolvedValue([appointment1, appointment2]);

      // Act
      const result = await appointmentRepository.findByEmail('<EMAIL>');

      // Assert
      expect(result).toHaveLength(2);
      expect(result[0].id).toBe('appointment-1');
      expect(result[1].id).toBe('appointment-2');
    });
  });

  describe('update', () => {
    it('should update appointment successfully', async () => {
      // Arrange
      const updatedData = {
        ...mockAppointmentData,
        status: 'CONFIRMED',
        updatedAt: new Date('2024-01-15T09:00:00Z'),
      };
      
      const updatedAppointment: Appointment = {
        ...mockAppointment,
        status: 'CONFIRMED',
        updatedAt: new Date('2024-01-15T09:00:00Z'),
      };

      vi.mocked(mockPrisma.appointment.update).mockResolvedValue(updatedData);

      // Act
      const result = await appointmentRepository.update('appointment-123', updatedAppointment);

      // Assert
      expect(result).toEqual(updatedAppointment);
      expect(mockPrisma.appointment.update).toHaveBeenCalledWith({
        where: { id: 'appointment-123' },
        data: expect.objectContaining({
          status: 'CONFIRMED',
        }),
      });
    });

    it('should handle update errors', async () => {
      // Arrange
      const dbError = new Error('Record not found');
      vi.mocked(mockPrisma.appointment.update).mockRejectedValue(dbError);

      // Act & Assert
      await expect(
        appointmentRepository.update('non-existent-id', mockAppointment)
      ).rejects.toThrow('Record not found');
    });
  });

  describe('delete', () => {
    it('should delete appointment successfully', async () => {
      // Arrange
      vi.mocked(mockPrisma.appointment.delete).mockResolvedValue(mockAppointmentData);

      // Act
      await appointmentRepository.delete('appointment-123');

      // Assert
      expect(mockPrisma.appointment.delete).toHaveBeenCalledWith({
        where: { id: 'appointment-123' },
      });
    });

    it('should handle delete errors', async () => {
      // Arrange
      const dbError = new Error('Record not found');
      vi.mocked(mockPrisma.appointment.delete).mockRejectedValue(dbError);

      // Act & Assert
      await expect(appointmentRepository.delete('non-existent-id'))
        .rejects.toThrow('Record not found');
    });
  });

  describe('data transformation', () => {
    it('should handle null values correctly', async () => {
      // Arrange
      const dataWithNulls = {
        ...mockAppointmentData,
        customerPhone: null,
        staffId: null,
        treatmentId: null,
        notes: null,
      };

      vi.mocked(mockPrisma.appointment.create).mockResolvedValue(dataWithNulls);

      const appointmentWithUndefined: Appointment = {
        ...mockAppointment,
        customerPhone: undefined,
        staffId: undefined,
        treatmentId: undefined,
        notes: undefined,
      };

      // Act
      const result = await appointmentRepository.create(appointmentWithUndefined);

      // Assert
      expect(result.customerPhone).toBeUndefined();
      expect(result.staffId).toBeUndefined();
      expect(result.treatmentId).toBeUndefined();
      expect(result.notes).toBeUndefined();
    });

    it('should preserve date objects', async () => {
      // Arrange
      vi.mocked(mockPrisma.appointment.create).mockResolvedValue(mockAppointmentData);

      // Act
      const result = await appointmentRepository.create(mockAppointment);

      // Assert
      expect(result.startTime).toBeInstanceOf(Date);
      expect(result.endTime).toBeInstanceOf(Date);
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });
  });
});
