import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AppointmentController } from '../../presentation/controllers/appointmentController';
import type { AppointmentService } from '../../application/services/appointmentService';
import type { Context } from 'hono';
import type { Appointment } from '@beauty-crm/platform-appointment-unified';

describe('AppointmentController', () => {
  let appointmentController: AppointmentController;
  let mockAppointmentService: AppointmentService;
  let mockContext: Context;

  const mockAppointment: Appointment = {
    id: 'appointment-123',
    salonId: 'salon-123',
    salonName: 'Test Salon',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    treatmentId: 'treatment-123',
    treatmentName: 'Haircut',
    treatmentDuration: 30,
    treatmentPrice: 35.0,
    staffId: 'staff-123',
    startTime: new Date('2024-01-15T10:00:00Z'),
    endTime: new Date('2024-01-15T10:30:00Z'),
    status: 'PENDING',
    notes: 'First time customer',
    createdAt: new Date('2024-01-15T08:00:00Z'),
    updatedAt: new Date('2024-01-15T08:00:00Z'),
  };

  beforeEach(() => {
    // Create mock service
    mockAppointmentService = {
      createAppointment: vi.fn(),
      getAvailableTimeSlots: vi.fn(),
      getAppointmentById: vi.fn(),
      updateAppointment: vi.fn(),
      cancelAppointment: vi.fn(),
    } as unknown as AppointmentService;

    // Create mock context
    mockContext = {
      req: {
        json: vi.fn(),
        query: vi.fn(),
        param: vi.fn(),
      },
      json: vi.fn(),
    } as unknown as Context;

    appointmentController = new AppointmentController(mockAppointmentService);
  });

  describe('createAppointment', () => {
    it('should create appointment successfully', async () => {
      // Arrange
      const requestData = {
        salonId: 'salon-123',
        salonName: 'Test Salon',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        treatmentDuration: 30,
        treatmentPrice: 35.0,
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
      };

      vi.mocked(mockContext.req.json).mockResolvedValue(requestData);
      vi.mocked(mockAppointmentService.createAppointment).mockResolvedValue(
        mockAppointment,
      );
      vi.mocked(mockContext.json).mockReturnValue({} as Response);

      // Act
      await appointmentController.createAppointment(mockContext);

      // Assert
      expect(mockContext.req.json).toHaveBeenCalledOnce();
      expect(mockAppointmentService.createAppointment).toHaveBeenCalledWith(
        requestData,
      );
      expect(mockContext.json).toHaveBeenCalledWith(
        { data: mockAppointment, success: true },
        201,
      );
    });

    it('should handle validation errors', async () => {
      // Arrange
      const invalidData = {
        customerName: 'John Doe',
        // Missing required fields
      };

      vi.mocked(mockContext.req.json).mockResolvedValue(invalidData);
      vi.mocked(mockContext.json).mockReturnValue({} as Response);

      // Act
      await appointmentController.createAppointment(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Invalid appointment data',
          details: expect.any(Array),
        }),
        400,
      );
    });

    it('should handle service errors', async () => {
      // Arrange
      const validData = {
        salonId: 'salon-123',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
      };

      vi.mocked(mockContext.req.json).mockResolvedValue(validData);
      vi.mocked(mockAppointmentService.createAppointment).mockRejectedValue(
        new Error('Database connection failed'),
      );
      vi.mocked(mockContext.json).mockReturnValue({} as Response);

      // Act
      await appointmentController.createAppointment(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Database connection failed',
        }),
        500,
      );
    });

    it('should handle unknown errors', async () => {
      // Arrange
      const validData = {
        salonId: 'salon-123',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
      };

      vi.mocked(mockContext.req.json).mockResolvedValue(validData);
      vi.mocked(mockAppointmentService.createAppointment).mockRejectedValue(
        'Unknown error',
      );
      vi.mocked(mockContext.json).mockReturnValue({} as Response);

      // Act
      await appointmentController.createAppointment(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Unknown error',
        }),
        500,
      );
    });
  });

  describe('getAvailableTimeSlots', () => {
    it('should return available time slots', async () => {
      // Arrange
      const mockSlots = [
        {
          startTime: '2024-01-15T09:00:00Z',
          endTime: '2024-01-15T09:30:00Z',
          available: true,
        },
        {
          startTime: '2024-01-15T09:30:00Z',
          endTime: '2024-01-15T10:00:00Z',
          available: false,
        },
      ];

      vi.mocked(mockContext.req.query).mockImplementation((key: string) => {
        const params: Record<string, string> = {
          salonId: 'salon-123',
          date: '2024-01-15',
          treatmentDuration: '30',
        };
        return params[key];
      });

      vi.mocked(mockAppointmentService.getAvailableTimeSlots).mockResolvedValue(
        mockSlots,
      );
      vi.mocked(mockContext.json).mockReturnValue({} as Response);

      // Act
      await appointmentController.getAvailableTimeSlots(mockContext);

      // Assert
      expect(mockAppointmentService.getAvailableTimeSlots).toHaveBeenCalledWith(
        {
          salonId: 'salon-123',
          date: '2024-01-15',
          treatmentDuration: 30,
        },
      );
      expect(mockContext.json).toHaveBeenCalledWith({
        success: true,
        data: mockSlots,
      });
    });

    it('should handle missing parameters', async () => {
      // Arrange
      vi.mocked(mockContext.req.query).mockImplementation((key: string) => {
        const params: Record<string, string | undefined> = {
          salonId: undefined, // Missing salonId
          date: '2024-01-15',
        };
        return params[key];
      });

      vi.mocked(mockContext.json).mockReturnValue({} as Response);

      // Act
      await appointmentController.getAvailableTimeSlots(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: expect.stringContaining('Missing required parameters'),
        }),
        400,
      );
    });

    it('should handle service errors for time slots', async () => {
      // Arrange
      vi.mocked(mockContext.req.query).mockImplementation((key: string) => {
        const params: Record<string, string> = {
          salonId: 'salon-123',
          date: '2024-01-15',
        };
        return params[key];
      });

      vi.mocked(mockAppointmentService.getAvailableTimeSlots).mockRejectedValue(
        new Error('Salon not found'),
      );
      vi.mocked(mockContext.json).mockReturnValue({} as Response);

      // Act
      await appointmentController.getAvailableTimeSlots(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Salon not found',
        }),
        500,
      );
    });

    it('should use default treatment duration', async () => {
      // Arrange
      vi.mocked(mockContext.req.query).mockImplementation((key: string) => {
        const params: Record<string, string | undefined> = {
          salonId: 'salon-123',
          date: '2024-01-15',
          treatmentDuration: undefined, // No duration provided
        };
        return params[key];
      });

      vi.mocked(mockAppointmentService.getAvailableTimeSlots).mockResolvedValue(
        [],
      );
      vi.mocked(mockContext.json).mockReturnValue({} as Response);

      // Act
      await appointmentController.getAvailableTimeSlots(mockContext);

      // Assert
      expect(mockAppointmentService.getAvailableTimeSlots).toHaveBeenCalledWith(
        {
          salonId: 'salon-123',
          date: '2024-01-15',
          treatmentDuration: 30, // Default value
        },
      );
    });
  });
});
