import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AppointmentRepository } from '../../infrastructure/repositories/appointmentRepository';
import { EmailService } from '../../infrastructure/services/emailService';
import { AppointmentController } from '../../presentation/controllers/appointmentController';
import type { PrismaClient } from '@prisma/client';
import type { Context } from 'hono';

describe('Simple Coverage Tests', () => {
  describe('AppointmentRepository', () => {
    let repository: AppointmentRepository;
    let mockPrisma: PrismaClient;

    beforeEach(() => {
      mockPrisma = ({
        appointment: {
          create: vi.fn(),
        },
      } as unknown) as PrismaClient;

      repository = new AppointmentRepository(mockPrisma);
    });

    it('should create appointment', async () => {
      // Arrange
      const appointmentData = {
        salonId: 'salon-123',
        customerName: '<PERSON>',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        startTime: new Date(),
        endTime: new Date(),
      };

      const mockResult = { id: 'appointment-123', ...appointmentData };
      (mockPrisma.appointment.create as any).mockResolvedValue(mockResult);

      // Act
      const result = await repository.create(appointmentData);

      // Assert
      expect(result).toEqual(mockResult);
      expect(mockPrisma.appointment.create).toHaveBeenCalledWith({
        data: appointmentData,
      });
    });

    it('should handle create errors', async () => {
      // Arrange
      const appointmentData = {
        salonId: 'salon-123',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        startTime: new Date(),
        endTime: new Date(),
      };

      (mockPrisma.appointment.create as any).mockRejectedValue(
        new Error('Database error')
      );

      // Act & Assert
      await expect(repository.create(appointmentData)).rejects.toThrow(
        'Database error'
      );
    });
  });

  describe('EmailService', () => {
    let emailService: EmailService;

    beforeEach(() => {
      emailService = new EmailService();
    });

    it('should send appointment confirmation', async () => {
      // Arrange
      const customerEmail = '<EMAIL>';
      const appointmentDetails = {
        id: 'appointment-123',
        customerName: 'John Doe',
        treatmentName: 'Haircut',
      };

      // Mock console.log to verify it's called
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      await emailService.sendAppointmentConfirmation(
        customerEmail,
        appointmentDetails
      );

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        'Sending appointment <NAME_EMAIL>',
        appointmentDetails
      );

      consoleSpy.mockRestore();
    });

    it('should handle different appointment details', async () => {
      // Arrange
      const customerEmail = '<EMAIL>';
      const appointmentDetails = {
        id: 'appointment-456',
        customerName: 'Jane Smith',
        treatmentName: 'Manicure',
        price: 50,
      };

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      await emailService.sendAppointmentConfirmation(
        customerEmail,
        appointmentDetails
      );

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        'Sending appointment <NAME_EMAIL>',
        appointmentDetails
      );

      consoleSpy.mockRestore();
    });
  });

  describe('AppointmentController', () => {
    let controller: AppointmentController;
    let mockAppointmentService: any;
    let mockContext: Context;

    beforeEach(() => {
      mockAppointmentService = {
        createAppointment: vi.fn(),
      };

      mockContext = ({
        req: {
          json: vi.fn(),
          query: vi.fn(),
        },
        json: vi.fn(),
      } as unknown) as Context;

      controller = new AppointmentController(mockAppointmentService);
    });

    it('should return available time slots', async () => {
      // Arrange
      (mockContext.json as any).mockReturnValue({} as Response);

      // Act
      await controller.getAvailableTimeSlots(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith({ slots: [] });
    });

    it('should handle appointment creation validation errors', async () => {
      // Arrange
      const invalidData = {
        customerName: 'John Doe',
        // Missing required fields
      };

      (mockContext.req.json as any).mockResolvedValue(invalidData);
      (mockContext.json as any).mockReturnValue({} as Response);

      // Act
      await controller.createAppointment(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Invalid appointment data',
          details: expect.any(Array),
        }),
        400
      );
    });

    it('should handle valid appointment creation', async () => {
      // Arrange
      const validData = {
        salonId: 'salon-123',
        customerId: 'c123456789012345678901234',
        treatmentId: 'c123456789012345678901234',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        treatmentDuration: 30,
        treatmentPrice: 35,
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
      };

      const mockAppointment = { id: 'appointment-123', ...validData };

      (mockContext.req.json as any).mockResolvedValue(validData);
      (mockAppointmentService.createAppointment as any).mockResolvedValue(
        mockAppointment
      );
      (mockContext.json as any).mockReturnValue({} as Response);

      // Act
      await controller.createAppointment(mockContext);

      // Assert
      expect(mockAppointmentService.createAppointment).toHaveBeenCalledWith(
        validData
      );
      expect(mockContext.json).toHaveBeenCalledWith(
        { data: mockAppointment, success: true },
        201
      );
    });

    it('should handle service errors', async () => {
      // Arrange
      const validData = {
        salonId: 'salon-123',
        customerId: 'c123456789012345678901234',
        treatmentId: 'c123456789012345678901234',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        treatmentDuration: 30,
        treatmentPrice: 35,
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
      };

      (mockContext.req.json as any).mockResolvedValue(validData);
      (mockAppointmentService.createAppointment as any).mockRejectedValue(
        new Error('Service error')
      );
      (mockContext.json as any).mockReturnValue({} as Response);

      // Act
      await controller.createAppointment(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Service error',
        }),
        500
      );
    });

    it('should handle unknown errors', async () => {
      // Arrange
      const validData = {
        salonId: 'salon-123',
        customerId: 'c123456789012345678901234',
        treatmentId: 'c123456789012345678901234',
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Haircut',
        treatmentDuration: 30,
        treatmentPrice: 35,
        startTime: '2024-01-15T10:00:00Z',
        endTime: '2024-01-15T10:30:00Z',
      };

      (mockContext.req.json as any).mockResolvedValue(validData);
      (mockAppointmentService.createAppointment as any).mockRejectedValue(
        'Unknown error'
      );
      (mockContext.json as any).mockReturnValue({} as Response);

      // Act
      await controller.createAppointment(mockContext);

      // Assert
      expect(mockContext.json).toHaveBeenCalledWith(
        expect.objectContaining({
          success: false,
          error: 'Unknown error',
        }),
        500
      );
    });
  });
});
