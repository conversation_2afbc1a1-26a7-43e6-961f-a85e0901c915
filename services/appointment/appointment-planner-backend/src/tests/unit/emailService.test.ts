import { describe, it, expect, beforeEach, vi } from 'vitest';
import { EmailService } from '../../infrastructure/services/emailService';
import type { Appointment } from '@beauty-crm/platform-appointment-unified';

// Mock nodemailer
vi.mock('nodemailer', () => ({
  createTransporter: vi.fn(() => ({
    sendMail: vi.fn(),
  })),
}));

describe('EmailService', () => {
  let emailService: EmailService;
  let mockTransporter: any;

  const mockAppointment: Appointment = {
    id: 'appointment-123',
    salonId: 'salon-123',
    salonName: 'Test Salon',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    treatmentId: 'treatment-123',
    treatmentName: 'Haircut',
    treatmentDuration: 30,
    treatmentPrice: 35.00,
    staffId: 'staff-123',
    startTime: new Date('2024-01-15T10:00:00Z'),
    endTime: new Date('2024-01-15T10:30:00Z'),
    status: 'PENDING',
    notes: 'First time customer',
    createdAt: new Date('2024-01-15T08:00:00Z'),
    updatedAt: new Date('2024-01-15T08:00:00Z'),
  };

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Create mock transporter
    mockTransporter = {
      sendMail: vi.fn(),
    };

    emailService = new EmailService();
    // Inject mock transporter
    (emailService as any).transporter = mockTransporter;
  });

  describe('sendAppointmentConfirmation', () => {
    it('should send confirmation email successfully', async () => {
      // Arrange
      mockTransporter.sendMail.mockResolvedValue({
        messageId: 'test-message-id',
        response: '250 OK',
      });

      // Act
      await emailService.sendAppointmentConfirmation(
        mockAppointment.customerEmail,
        mockAppointment
      );

      // Assert
      expect(mockTransporter.sendMail).toHaveBeenCalledOnce();
      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: expect.stringContaining('Appointment Confirmation'),
          html: expect.stringContaining('John Doe'),
          text: expect.stringContaining('John Doe'),
        })
      );
    });

    it('should include appointment details in email', async () => {
      // Arrange
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-id' });

      // Act
      await emailService.sendAppointmentConfirmation(
        mockAppointment.customerEmail,
        mockAppointment
      );

      // Assert
      const emailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(emailCall.html).toContain('Test Salon');
      expect(emailCall.html).toContain('Haircut');
      expect(emailCall.html).toContain('$35.00');
      expect(emailCall.html).toContain('January 15, 2024');
      expect(emailCall.text).toContain('Test Salon');
      expect(emailCall.text).toContain('Haircut');
    });

    it('should handle email sending errors', async () => {
      // Arrange
      const emailError = new Error('SMTP connection failed');
      mockTransporter.sendMail.mockRejectedValue(emailError);

      // Act & Assert
      await expect(
        emailService.sendAppointmentConfirmation(
          mockAppointment.customerEmail,
          mockAppointment
        )
      ).rejects.toThrow('SMTP connection failed');
    });

    it('should format appointment time correctly', async () => {
      // Arrange
      const appointmentWithSpecificTime: Appointment = {
        ...mockAppointment,
        startTime: new Date('2024-06-15T14:30:00Z'),
        endTime: new Date('2024-06-15T15:00:00Z'),
      };

      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-id' });

      // Act
      await emailService.sendAppointmentConfirmation(
        appointmentWithSpecificTime.customerEmail,
        appointmentWithSpecificTime
      );

      // Assert
      const emailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(emailCall.html).toContain('June 15, 2024');
      expect(emailCall.html).toContain('2:30 PM');
    });

    it('should handle appointments without phone number', async () => {
      // Arrange
      const appointmentWithoutPhone: Appointment = {
        ...mockAppointment,
        customerPhone: undefined,
      };

      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-id' });

      // Act
      await emailService.sendAppointmentConfirmation(
        appointmentWithoutPhone.customerEmail,
        appointmentWithoutPhone
      );

      // Assert
      expect(mockTransporter.sendMail).toHaveBeenCalledOnce();
      const emailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(emailCall.html).toContain('John Doe');
    });

    it('should handle appointments without notes', async () => {
      // Arrange
      const appointmentWithoutNotes: Appointment = {
        ...mockAppointment,
        notes: undefined,
      };

      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-id' });

      // Act
      await emailService.sendAppointmentConfirmation(
        appointmentWithoutNotes.customerEmail,
        appointmentWithoutNotes
      );

      // Assert
      expect(mockTransporter.sendMail).toHaveBeenCalledOnce();
      const emailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(emailCall.html).toContain('John Doe');
    });
  });

  describe('sendAppointmentReminder', () => {
    it('should send reminder email successfully', async () => {
      // Arrange
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'reminder-id' });

      // Act
      await emailService.sendAppointmentReminder(
        mockAppointment.customerEmail,
        mockAppointment
      );

      // Assert
      expect(mockTransporter.sendMail).toHaveBeenCalledOnce();
      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: expect.stringContaining('Appointment Reminder'),
          html: expect.stringContaining('reminder'),
          text: expect.stringContaining('reminder'),
        })
      );
    });

    it('should include reminder-specific content', async () => {
      // Arrange
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'reminder-id' });

      // Act
      await emailService.sendAppointmentReminder(
        mockAppointment.customerEmail,
        mockAppointment
      );

      // Assert
      const emailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(emailCall.html).toContain('upcoming appointment');
      expect(emailCall.html).toContain('tomorrow');
    });
  });

  describe('sendAppointmentCancellation', () => {
    it('should send cancellation email successfully', async () => {
      // Arrange
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'cancel-id' });

      // Act
      await emailService.sendAppointmentCancellation(
        mockAppointment.customerEmail,
        mockAppointment
      );

      // Assert
      expect(mockTransporter.sendMail).toHaveBeenCalledOnce();
      expect(mockTransporter.sendMail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: '<EMAIL>',
          subject: expect.stringContaining('Appointment Cancelled'),
          html: expect.stringContaining('cancelled'),
          text: expect.stringContaining('cancelled'),
        })
      );
    });

    it('should include cancellation-specific content', async () => {
      // Arrange
      mockTransporter.sendMail.mockResolvedValue({ messageId: 'cancel-id' });

      // Act
      await emailService.sendAppointmentCancellation(
        mockAppointment.customerEmail,
        mockAppointment
      );

      // Assert
      const emailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(emailCall.html).toContain('has been cancelled');
      expect(emailCall.html).toContain('reschedule');
    });
  });

  describe('email formatting', () => {
    it('should format currency correctly', async () => {
      // Arrange
      const expensiveAppointment: Appointment = {
        ...mockAppointment,
        treatmentPrice: 125.50,
      };

      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-id' });

      // Act
      await emailService.sendAppointmentConfirmation(
        expensiveAppointment.customerEmail,
        expensiveAppointment
      );

      // Assert
      const emailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(emailCall.html).toContain('$125.50');
    });

    it('should format duration correctly', async () => {
      // Arrange
      const longAppointment: Appointment = {
        ...mockAppointment,
        treatmentDuration: 90,
        treatmentName: 'Full Color Treatment',
      };

      mockTransporter.sendMail.mockResolvedValue({ messageId: 'test-id' });

      // Act
      await emailService.sendAppointmentConfirmation(
        longAppointment.customerEmail,
        longAppointment
      );

      // Assert
      const emailCall = mockTransporter.sendMail.mock.calls[0][0];
      expect(emailCall.html).toContain('90 minutes');
      expect(emailCall.html).toContain('Full Color Treatment');
    });
  });
});
