import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AppointmentService } from '../../application/services/appointmentService';
import type { AppointmentRepository } from '../../infrastructure/repositories/appointmentRepository';
import type { EmailService } from '../../infrastructure/services/emailService';
import type { OutboxRelayer } from '../../infrastructure/events/OutboxRelayer';
import type {
  CreateAppointmentRequest,
  Appointment,
} from '@beauty-crm/platform-appointment-unified';

// Mock the createAppointmentDomain function
const mockCreateAppointmentDomain = vi.fn();
vi.mock('@beauty-crm/platform-appointment-unified', async () => {
  const actual = await vi.importActual(
    '@beauty-crm/platform-appointment-unified'
  );
  return {
    ...actual,
    createAppointmentDomain: mockCreateAppointmentDomain,
  };
});

// Mock the global createAppointmentDomain function
global.createAppointmentDomain = mockCreateAppointmentDomain;

describe('AppointmentService', () => {
  let appointmentService: AppointmentService;
  let mockAppointmentRepository: AppointmentRepository;
  let mockEmailService: EmailService;
  let mockOutboxRelayer: OutboxRelayer;

  const mockAppointment: Appointment = {
    id: 'appointment-123',
    salonId: 'salon-123',
    salonName: 'Test Salon',
    customerName: 'John Doe',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    treatmentId: 'treatment-123',
    treatmentName: 'Haircut',
    treatmentDuration: 30,
    treatmentPrice: 35.0,
    staffId: 'staff-123',
    startTime: new Date('2024-01-15T10:00:00Z'),
    endTime: new Date('2024-01-15T10:30:00Z'),
    status: 'PENDING',
    notes: 'First time customer',
    createdAt: new Date('2024-01-15T08:00:00Z'),
    updatedAt: new Date('2024-01-15T08:00:00Z'),
  };

  const mockCreateRequest: CreateAppointmentRequest = {
    salonId: 'salon-123',
    salonName: 'Test Salon',
    customerName: 'John Doe',
    customerEmail: '<EMAIL>',
    customerPhone: '+1234567890',
    treatmentId: 'treatment-123',
    treatmentName: 'Haircut',
    treatmentDuration: 30,
    treatmentPrice: 35.0,
    staffId: 'staff-123',
    startTime: new Date('2024-01-15T10:00:00Z'),
    endTime: new Date('2024-01-15T10:30:00Z'),
    notes: 'First time customer',
  };

  beforeEach(() => {
    // Create mocks
    mockAppointmentRepository = ({
      create: vi.fn(),
    } as unknown) as AppointmentRepository;

    mockEmailService = ({
      sendAppointmentConfirmation: vi.fn(),
    } as unknown) as EmailService;

    mockOutboxRelayer = ({
      add: vi.fn(),
      start: vi.fn(),
      stop: vi.fn(),
    } as unknown) as OutboxRelayer;

    // Mock the domain function
    mockCreateAppointmentDomain.mockReturnValue({
      toAppointment: () => mockAppointment,
    });

    appointmentService = new AppointmentService(
      mockAppointmentRepository,
      mockEmailService,
      mockOutboxRelayer
    );
  });

  describe('createAppointment', () => {
    it('should create appointment successfully', async () => {
      // Arrange
      vi.mocked(mockAppointmentRepository.create).mockResolvedValue(
        mockAppointment
      );
      vi.mocked(mockEmailService.sendAppointmentConfirmation).mockResolvedValue(
        undefined
      );

      // Act
      const result = await appointmentService.createAppointment(
        mockCreateRequest
      );

      // Assert
      expect(result).toEqual(mockAppointment);
      expect(mockAppointmentRepository.create).toHaveBeenCalledOnce();
      expect(mockEmailService.sendAppointmentConfirmation).toHaveBeenCalledWith(
        mockAppointment.customerEmail,
        mockAppointment
      );
    });

    it('should handle repository errors', async () => {
      // Arrange
      const error = new Error('Database connection failed');
      vi.mocked(mockAppointmentRepository.create).mockRejectedValue(error);

      // Act & Assert
      await expect(
        appointmentService.createAppointment(mockCreateRequest)
      ).rejects.toThrow('Database connection failed');

      expect(
        mockEmailService.sendAppointmentConfirmation
      ).not.toHaveBeenCalled();
    });

    it('should handle email service errors gracefully', async () => {
      // Arrange
      vi.mocked(mockAppointmentRepository.create).mockResolvedValue(
        mockAppointment
      );
      vi.mocked(mockEmailService.sendAppointmentConfirmation).mockRejectedValue(
        new Error('Email service unavailable')
      );

      // Act & Assert
      await expect(
        appointmentService.createAppointment(mockCreateRequest)
      ).rejects.toThrow('Email service unavailable');

      expect(mockAppointmentRepository.create).toHaveBeenCalledOnce();
    });

    it('should create appointment with minimal data', async () => {
      // Arrange
      const minimalRequest: CreateAppointmentRequest = {
        salonId: 'salon-123',
        salonName: 'Test Salon',
        customerName: 'Jane Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Basic Cut',
        treatmentDuration: 30,
        treatmentPrice: 25.0,
        startTime: new Date('2024-01-15T14:00:00Z'),
        endTime: new Date('2024-01-15T14:30:00Z'),
      };

      const minimalAppointment: Appointment = {
        ...mockAppointment,
        customerName: 'Jane Doe',
        customerEmail: '<EMAIL>',
        treatmentName: 'Basic Cut',
        treatmentPrice: 25.0,
        startTime: new Date('2024-01-15T14:00:00Z'),
        endTime: new Date('2024-01-15T14:30:00Z'),
        customerPhone: undefined,
        staffId: undefined,
        notes: undefined,
      };

      vi.mocked(mockAppointmentRepository.create).mockResolvedValue(
        minimalAppointment
      );
      vi.mocked(mockEmailService.sendAppointmentConfirmation).mockResolvedValue(
        undefined
      );

      // Act
      const result = await appointmentService.createAppointment(minimalRequest);

      // Assert
      expect(result).toEqual(minimalAppointment);
      expect(mockAppointmentRepository.create).toHaveBeenCalledOnce();
    });

    it('should handle different appointment statuses', async () => {
      // Arrange
      const confirmedAppointment: Appointment = {
        ...mockAppointment,
        status: 'CONFIRMED',
      };

      vi.mocked(mockAppointmentRepository.create).mockResolvedValue(
        confirmedAppointment
      );
      vi.mocked(mockEmailService.sendAppointmentConfirmation).mockResolvedValue(
        undefined
      );

      // Act
      const result = await appointmentService.createAppointment(
        mockCreateRequest
      );

      // Assert
      expect(result.status).toBe('CONFIRMED');
      expect(mockEmailService.sendAppointmentConfirmation).toHaveBeenCalledWith(
        confirmedAppointment.customerEmail,
        confirmedAppointment
      );
    });

    it('should handle future appointment times', async () => {
      // Arrange
      const futureRequest: CreateAppointmentRequest = {
        ...mockCreateRequest,
        startTime: new Date('2024-12-25T10:00:00Z'),
        endTime: new Date('2024-12-25T10:30:00Z'),
      };

      const futureAppointment: Appointment = {
        ...mockAppointment,
        startTime: new Date('2024-12-25T10:00:00Z'),
        endTime: new Date('2024-12-25T10:30:00Z'),
      };

      vi.mocked(mockAppointmentRepository.create).mockResolvedValue(
        futureAppointment
      );
      vi.mocked(mockEmailService.sendAppointmentConfirmation).mockResolvedValue(
        undefined
      );

      // Act
      const result = await appointmentService.createAppointment(futureRequest);

      // Assert
      expect(result.startTime).toEqual(new Date('2024-12-25T10:00:00Z'));
      expect(result.endTime).toEqual(new Date('2024-12-25T10:30:00Z'));
    });

    it('should handle different treatment durations', async () => {
      // Arrange
      const longTreatmentRequest: CreateAppointmentRequest = {
        ...mockCreateRequest,
        treatmentName: 'Full Color Treatment',
        treatmentDuration: 120,
        treatmentPrice: 150.0,
        endTime: new Date('2024-01-15T12:00:00Z'),
      };

      const longTreatmentAppointment: Appointment = {
        ...mockAppointment,
        treatmentName: 'Full Color Treatment',
        treatmentDuration: 120,
        treatmentPrice: 150.0,
        endTime: new Date('2024-01-15T12:00:00Z'),
      };

      vi.mocked(mockAppointmentRepository.create).mockResolvedValue(
        longTreatmentAppointment
      );
      vi.mocked(mockEmailService.sendAppointmentConfirmation).mockResolvedValue(
        undefined
      );

      // Act
      const result = await appointmentService.createAppointment(
        longTreatmentRequest
      );

      // Assert
      expect(result.treatmentDuration).toBe(120);
      expect(result.treatmentPrice).toBe(150.0);
      expect(result.treatmentName).toBe('Full Color Treatment');
    });
  });
});
