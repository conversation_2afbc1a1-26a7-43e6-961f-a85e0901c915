import type { Context } from 'hono';
import { z } from 'zod';
import type { AppointmentService } from '../../application/services/appointmentService';
import { type CreateAppointmentRequest } from '@beauty-crm/platform-appointment-unified';

const CUID_REGEX = /^c[a-z0-9]{24}$/;

const appointmentSchema = z.object({
  salonId: z.string().min(1, 'Salon ID is required'),
  customerId: z
    .string()
    .regex(CUID_REGEX, 'Customer ID must be a valid CUID format'),
  staffId: z.string().regex(CUID_REGEX, 'Staff ID must be a valid CUID format').optional(),
  treatmentId: z
    .string()
    .regex(CUID_REGEX, 'Treatment ID must be a valid CUID format'),
  customerName: z.string().min(1, 'Customer name is required'),
  customerEmail: z.string().email('Valid email is required'),
  customerPhone: z.string().optional(),
  treatmentName: z.string().min(1, 'Treatment name is required'),
  treatmentDuration: z.number().positive('Duration must be positive'),
  treatmentPrice: z.number().positive('Price must be positive'),
  startTime: z.string().datetime('Valid start time is required'),
  endTime: z.string().datetime('Valid end time is required'),
  status: z.enum(['PENDING', 'CONFIRMED', 'SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'RESCHEDULED']).optional(),
  notes: z.string().optional(),
  locale: z.string().optional(),
  source: z.string().optional(),
});

export class AppointmentController {
  constructor(private appointmentService: AppointmentService) {}

  createAppointment = async (c: Context) => {
    try {
      const body = await c.req.json();

      const validationResult = appointmentSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.errors,
            error: 'Invalid appointment data',
            success: false,
          },
          400,
        );
      }

      const appointment = await this.appointmentService.createAppointment(
        validationResult.data,
      );

      return c.json({ data: appointment, success: true }, 201);
    } catch (error) {
      console.error('Error creating appointment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500,
      );
    }
  };

  getAvailableTimeSlots = async (c: Context) => {
    // TODO: Implement
    return c.json({ slots: [] });
  };
}
