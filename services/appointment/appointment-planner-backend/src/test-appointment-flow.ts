import { AppointmentDomainService } from '@beauty-crm/platform-appointment-unified/src/services/AppointmentDomainService';
import type { CreateAppointmentRequest } from '@beauty-crm/platform-appointment-unified';
import { APPOINTMENT_STATUSES } from '@beauty-crm/platform-appointment-unified';

// Instantiate the domain service
const appointmentDomainService = new AppointmentDomainService();

async function runAppointmentFlow() {
  console.log('--- Starting Appointment Flow Test ---');

  try {
    // Example: Create a new appointment request
    const newAppointmentRequest: CreateAppointmentRequest = {
      salonId: 'salon-001',
      customerId: 'customer-abc',
      treatmentId: 'treatment-101',
      staffId: 'staff-xyz',
      startTime: new Date('2025-12-01T10:00:00Z'),
      endTime: new Date('2025-12-01T11:00:00Z'),
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      customerPhone: '************',
      treatmentName: 'Haircut',
      treatmentDuration: 60,
      treatmentPrice: 50.0,
      salonName: 'Beauty Salon',
      salonLogo: 'http://example.com/logo.png',
      salonColor: '#FFFFFF',
      notes: 'Customer requested a specific stylist.',
      status: APPOINTMENT_STATUSES.PENDING,
      locale: 'en-US',
      source: 'planner',
    };

    console.log(
      'Created Appointment Request:',
      JSON.stringify(newAppointmentRequest, null, 2)
    );

    // Example: Use the domain service to process the appointment creation
    // In a real scenario, you would fetch existing appointments for the staff member
    // for the given day to check for conflicts.
    console.log(
      'Calling AppointmentDomainService.processAppointmentCreation...'
    );
    const newAppointmentDomain = appointmentDomainService.processAppointmentCreation(
      newAppointmentRequest,
      [] // No existing appointments for this test
    );

    console.log(
      'Appointment processed successfully by domain service:',
      JSON.stringify(newAppointmentDomain.toAppointment(), null, 2)
    );

    console.log('--- Appointment Flow Test Completed Successfully ---');
  } catch (error) {
    console.error('--- Appointment Flow Test Failed ---', error);
  }
}

runAppointmentFlow();
