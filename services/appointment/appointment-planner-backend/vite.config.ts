import path from 'node:path';
import { VitePluginNode } from 'vite-plugin-node';
import { defineConfig } from 'vitest/config';

export default defineConfig({
  plugins: [
    ...VitePluginNode({
      adapter: 'express',
      appPath: './src/index.ts',
      exportName: 'app',
      tsCompiler: 'swc',
    }),
  ],
  resolve: {
    alias: {
      '@application': path.resolve(__dirname, './src/application'),
      '@beauty-crm/platform-logger': path.resolve(
        __dirname,
        '../../../shared-platform-engineering/platform-logger/src'
      ),
      '@beauty-crm/product-domain-types': path.resolve(
        __dirname,
        '../../../shared-product-engineering/product-domain-types/src'
      ),
      '@beauty-crm/product-identity-types': path.resolve(
        __dirname,
        '../../../shared-product-engineering/product-identity-types/src'
      ),
      '@domain': path.resolve(__dirname, './src/domain'),
      '@infrastructure': path.resolve(__dirname, './src/infrastructure'),
      '@presentation': path.resolve(__dirname, './src/presentation'),
      '@test': path.resolve(__dirname, './src/tests'),
    },
  },
  server: {
    port: 5017,
  },
  test: {
    coverage: {
      all: true,
      exclude: [
        'node_modules/**',
        'dist/**',
        'docs/**',
        'src/tests/**',
        'src/**/*.test.ts',
        'src/**/*.spec.ts',
        'src/**/*.d.ts',
        'src/test-*.ts',
        'prisma/**',
        '**/*.config.{ts,js}',
        '**/coverage/**',
      ],
      include: ['src/**/*.{ts,js}'],
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      thresholds: {
        branches: 70,
        functions: 70,
        lines: 70,
        statements: 70,
      },
    },
    deps: {
      inline: [
        '@beauty-crm/product-identity-types',
        '@beauty-crm/platform-logger',
        '@beauty-crm/platform-db-client',
        '@beauty-crm/product-domain-types',
      ],
    },
    environment: 'node',
    exclude: ['node_modules', 'dist', 'docs/node_modules', '**/tests/e2e/**'],
    globals: true,
    include: ['src/**/*.{spec,test}.{ts,js}'],
  },
});
