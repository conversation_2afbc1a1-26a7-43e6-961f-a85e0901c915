{"dependencies": {"@beauty-crm/platform-appointment-unified": "workspace:*", "@beauty-crm/platform-computing-lifecycle": "workspace:*", "@beauty-crm/platform-eventing": "workspace:*", "@hono/node-server": "^1.16.0", "@hono/zod-validator": "^0.7.1", "@paralleldrive/cuid2": "^2.2.2", "@prisma/client": "6.11.1", "axios": "^1.10.0", "hono": "^4.8.5", "i18next": "^25.3.2", "nodemailer": "^7.0.5", "zod": "^3.25.76"}, "description": "Appointment Planner Backend Appointment API and Available Slots API synchronised with appointment backend", "devDependencies": {"@cucumber/cucumber": "^11.3.0", "@playwright/test": "^1.54.1", "@types/chai": "^5.2.2", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.16.4", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@vitest/coverage-v8": "^3.2.4", "bun-types": "^1.2.18", "chai": "^5.2.1", "prisma": "^6.12.0", "smtp-tester": "^2.1.0", "supertest": "^7.1.3", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-node": "^5.0.1", "vitest": "^3.2.4"}, "files": ["dist"], "keywords": ["appointment", "planner", "appointment", "beauty"], "main": "dist/index.js", "name": "@beauty-crm/appointment-planner-backend", "prisma": {"packageManager": "bun"}, "scripts": {"build": "bun x tsc --project tsconfig.json && mkdir -p dist", "build:test": "tsc --project tsconfig.test.json", "dev": "DATABASE_URL=*********************************************************/beauty_crm PORT=5016 bun --hot src/index.ts", "prisma:generate": "bunx prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:migrate:reset": "prisma migrate reset", "prisma:push": "prisma db push", "prisma:studio": "prisma studio", "start": "bun run dist/index.js", "worker": "bun run src/infrastructure/outbox-worker.ts", "test:cucumber": "concurrently \"bun run test:cucumber:double-appointment\" \"bun run test:cucumber:double-appointment-bug\" \"bun run test:cucumber:email\" \"bun run test:cucumber:cancellation\" \"bun run test:cucumber:fully-booked\" \"bun run test:cucumber:slow-appointment\" \"bun run test:cucumber:amsterdam\" \"bun run test:cucumber:breda\" \"bun run test:cucumber:newyork\"", "test:cucumber:amsterdam": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/timezone-amsterdam/amsterdam-appointment.feature --import src/tests/features/timezone-amsterdam/amsterdam-appointment.steps.ts", "test:cucumber:breda": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/timezone-breda/breda-appointment.feature --import src/tests/features/timezone-breda/breda-appointment.steps.ts", "test:cucumber:cancellation": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/cancellation/cancellation.feature --import src/tests/features/cancellation/cancellation.steps.ts", "test:cucumber:double-appointment": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/double-appointment/double-appointment.feature --import src/tests/features/double-appointment/double-appointment.steps.ts", "test:cucumber:double-appointment-bug": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/double-appointment/double-appointment.feature --import src/tests/features/double-appointment/double-appointment-bug.steps.ts", "test:cucumber:email": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/email/email.feature --import src/tests/features/email/email.steps.ts", "test:cucumber:fully-booked": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/fully-booked/fully-booked.feature --import src/tests/features/fully-booked/fully-booked.steps.ts", "test:cucumber:newyork": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/timezone-newyork/newyork-appointment.feature --import src/tests/features/timezone-newyork/newyork-appointment.steps.ts", "test:cucumber:slow-appointment": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/slow-appointment/slow-appointment.feature --import src/tests/features/slow-appointment/slow-appointment.steps.ts", "test:cucumber:sprint6": "concurrently \"bun run test:cucumber:sprint6-salon-registration\" \"bun run test:cucumber:sprint6-appointment-booking\"", "test:cucumber:sprint6-appointment-booking": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/netherlands-market/dutch-appointment-booking.feature --import src/tests/features/netherlands-market/dutch-appointment-booking.steps.ts", "test:cucumber:sprint6-salon-registration": "NODE_OPTIONS=\"--loader ts-node/esm\" cucumber-js src/tests/features/netherlands-market/dutch-salon-registration.feature --import src/tests/features/netherlands-market/dutch-salon-registration.steps.ts", "test:service:email": "bun test src/infrastructure/services/emailService.test.ts", "test:api": "vitest run src/tests/api/", "test:api:watch": "vitest watch src/tests/api/", "test:supertest": "vitest run src/tests/api/supertest-integration.test.ts", "test:supertest:watch": "vitest watch src/tests/api/supertest-integration.test.ts"}, "type": "module", "version": "1.0.0"}