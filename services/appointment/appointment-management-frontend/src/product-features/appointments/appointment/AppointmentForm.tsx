import {
  createEvent,
  EventPublisher,
  EventTypes,
  PublisherConfigs,
} from '@beauty-crm/platform-eventing';
import type { Appointment } from '@beauty-crm/platform-appointment-unified';
import { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';
import type { Client, Staff, Station, Treatment } from '../types';

// Define types for the simplified form
type TimeSlot = {
  id: string;
  start: string;
  end: string;
};

type ClientDetails = {
  name: string;
  phone: string;
  email: string;
};

type Appointment = {
  id: string;
  customerId: string;
  staffId: string;
  treatmentId: string;
  stationId: string;
  startTime: string;
  endTime: string;
  clientDetails?: ClientDetails;
  status?: string;
  notes?: string;
};

interface AppointmentFormProps {
  clients: Client[];
  staff: Staff[];
  treatments: Treatment[];
  stations: Station[];
  initialDate: Date;
  initialTime: string;
  onSubmit: (values: Record<string, unknown>) => Promise<boolean>;
  onCancel: () => void;

  // Optional props for the simplified form
  availableTreatments?: Array<{
    id: string;
    name: string;
    durationMinutes: number;
    price: number;
  }>;
  availableTimeSlots?: TimeSlot[];
  bookedTimeSlots?: TimeSlot[];
  onSuccess?: (appointment: Appointment) => void;
  onError?: (error: string) => void;
}

export const AppointmentForm = ({
  clients,
  staff,
  treatments,
  _stations,
  availableTreatments,
  availableTimeSlots,
  bookedTimeSlots,
  onSuccess,
  onError,
}: AppointmentFormProps) => {
  // Use actual treatments data, fallback to availableTreatments if provided, otherwise mock
  const actualTreatments =
    treatments?.length > 0
      ? treatments.map((treatment) => ({
          durationMinutes: treatment.durationMinutes,
          id: treatment.id,
          name: treatment.name,
          price: treatment.price,
        }))
      : availableTreatments || [
          {
            durationMinutes: 60,
            id: 'treatment-1',
            name: 'Haircut',
            price: 50,
          },
          {
            durationMinutes: 120,
            id: 'treatment-2',
            name: 'Hair Coloring',
            price: 120,
          },
          {
            durationMinutes: 45,
            id: 'treatment-3',
            name: 'Styling',
            price: 40,
          },
          { durationMinutes: 75, id: 'treatment-4', name: 'Facial', price: 85 },
          {
            durationMinutes: 30,
            id: 'treatment-5',
            name: 'Manicure',
            price: 35,
          },
        ];

  // Use actual staff data with fallback
  const actualStaff =
    staff?.length > 0
      ? staff
      : [
          {
            email: '<EMAIL>',
            id: 'staff-1',
            name: 'Jessica Taylor',
            phone: '555-0101',
          },
          {
            email: '<EMAIL>',
            id: 'staff-2',
            name: 'Mike Johnson',
            phone: '555-0102',
          },
          {
            email: '<EMAIL>',
            id: 'staff-3',
            name: 'Sarah Wilson',
            phone: '555-0103',
          },
        ];

  // Generate mock time slots for today
  const generateTimeSlots = (): TimeSlot[] => {
    const slots: TimeSlot[] = [];
    const today = new Date();
    for (let hour = 9; hour < 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const start = new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate(),
          hour,
          minute,
        );
        const end = new Date(start.getTime() + 30 * 60000); // 30 minutes
        slots.push({
          end: end.toISOString(),
          id: `slot-${hour}-${minute}`,
          start: start.toISOString(),
        });
      }
    }
    return slots;
  };

  const mockTimeSlots = availableTimeSlots || generateTimeSlots();
  const mockBookedSlots = bookedTimeSlots || [];

  // State for the appointment form
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [selectedTreatment, setSelectedTreatment] = useState<string>('');
  const [selectedStaff, setSelectedStaff] = useState<string>('');
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>('');
  const [clientDetails, setClientDetails] = useState<ClientDetails>({
    email: '',
    name: '',
    phone: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [confirmationMessage, setConfirmationMessage] = useState<string>('');

  // Client details validation schema
  const clientSchema = z.object({
    email: z.string().email('Invalid email address'),
    name: z.string().min(1, 'Name is required'),
    phone: z
      .string()
      .regex(/^\d{10}$/, 'Invalid phone number - must be 10 digits'),
  });

  // Handle new appointment button click
  const handleNewAppointment = () => {
    setIsCreatingNew(true);
    setSelectedTreatment('');
    setSelectedStaff('');
    setSelectedTimeSlot('');
    setClientDetails({ email: '', name: '', phone: '' });
    setErrors({});
    setConfirmationMessage('');
  };

  // Handle treatment selection
  const handleTreatmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedTreatment(e.target.value);
    // Clear treatment-related errors
    const { treatment: _treatmentError, ...restErrors } = errors;
    setErrors(restErrors);
  };

  // Handle staff selection
  const handleStaffChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedStaff(e.target.value);
    // Clear staff-related errors
    const { staff: _staffError, ...restErrors } = errors;
    setErrors(restErrors);
  };

  // Handle time slot selection
  const handleTimeSlotChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const timeSlotId = e.target.value;
    setSelectedTimeSlot(timeSlotId);

    // Check if the time slot is already booked
    const isBooked = mockBookedSlots.some((slot) => slot.id === timeSlotId);
    if (isBooked) {
      setErrors((prev) => ({
        ...prev,
        timeSlot: 'This time slot is already booked',
      }));
    } else {
      const { timeSlot: _timeSlotError, ...restErrors } = errors;
      setErrors(restErrors);
    }
  };

  // Handle client details changes
  const handleClientDetailsChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const { name, value } = e.target;
    setClientDetails((prev) => ({ ...prev, [name]: value }));

    // Clear field-specific errors as user types
    const { [name]: _fieldError, ...restErrors } = errors;
    setErrors(restErrors);
  };

  // Handle appointment confirmation
  const handleConfirm = async () => {
    // Reset errors
    setErrors({});

    // Validate required fields
    const newErrors: Record<string, string> = {};

    if (!selectedTreatment) {
      newErrors.treatment = 'Please select a treatment';
    }

    if (!selectedStaff) {
      newErrors.staff = 'Please select a staff member';
    }

    if (!selectedTimeSlot) {
      newErrors.timeSlot = 'Please select a time slot';
    }

    // Validate client details
    try {
      clientSchema.parse(clientDetails);
    } catch (error) {
      if (error instanceof z.ZodError) {
        for (const err of error.errors) {
          if (err.path[0]) {
            newErrors[err.path[0].toString()] = err.message;
          }
        }
      }
    }

    // If there are validation errors, display them
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      onError?.('Please fix the validation errors');
      return;
    }

    // Check if the time slot is already booked
    const isBooked = mockBookedSlots.some(
      (slot) => slot.id === selectedTimeSlot,
    );
    if (isBooked) {
      setErrors({ timeSlot: 'Time slot already booked' });
      onError?.('Time slot already booked');
      return;
    }

    // Create the appointment
    const selectedSlot = mockTimeSlots.find(
      (slot) => slot.id === selectedTimeSlot,
    );
    if (!selectedSlot) {
      setErrors({ timeSlot: 'Invalid time slot selected' });
      onError?.('Invalid time slot selected');
      return;
    }
    const selectedTreatmentName =
      actualTreatments.find((s) => s.id === selectedTreatment)?.name ||
      'Selected Treatment';
    const newAppointment: UnifiedAppointment = {
      createdAt: new Date(),
      customerEmail: clientDetails.email, // Assuming client ID is available or generate new
      customerId: clients[0]?.id || uuidv4(),
      customerName: clientDetails.name,
      customerPhone: clientDetails.phone,
      endTime: new Date(selectedSlot.end),
      id: uuidv4(),
      salonId: 'default-salon-id',
      salonName: 'Default Salon',
      staffId: selectedStaff,
      startTime: new Date(selectedSlot.start),
      status: 'booked',
      treatmentDuration:
        actualTreatments.find((t) => t.id === selectedTreatment)
          ?.durationMinutes || 0, // Placeholder
      treatmentName: selectedTreatmentName, // Placeholder
      treatmentPrice:
        actualTreatments.find((t) => t.id === selectedTreatment)?.price || 0,
      updatedAt: new Date(),
    };

    try {
      const appointmentCreatedEvent = createEvent()
        .type(EventTypes.created('appointment'))
        .aggregate(newAppointment.id, 'appointment')
        .data(newAppointment)
        .source('management-frontend')
        .build();

      const publisher = new EventPublisher(
        PublisherConfigs.appointment('management-frontend'),
      );
      await publisher.connect();
      await publisher.publish(appointmentCreatedEvent);
      await publisher.disconnect();

      onSuccess?.(newAppointment);
    } catch (error) {
      console.error('Error publishing appointment event:', error);
      onError?.('Failed to create appointment. Please try again.');
    }

    // Show confirmation message

    const selectedStaffName =
      actualStaff.find((s) => s.id === selectedStaff)?.name || 'Selected Staff';
    setConfirmationMessage(
      `Appointment created successfully! ${selectedTreatmentName} with ${selectedStaffName}`,
    );

    // Reset form after a delay
    setTimeout(() => {
      setIsCreatingNew(false);
      setConfirmationMessage('');
    }, 2000);
  };

  return (
    <div className="appointment-form" data-testid="appointment-form-container">
      {!isCreatingNew ? (
        <div
          className="new-appointment-trigger"
          data-testid="new-appointment-trigger"
        >
          <button
            type="button"
            onClick={handleNewAppointment}
            data-testid="new-appointment-button"
            aria-label="Create New Appointment - Click to open form"
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded font-medium"
          >
            ➕ New Appointment
          </button>
        </div>
      ) : (
        <div
          className="form-container space-y-4"
          data-testid="appointment-form-fields"
        >
          <div className="form-header" data-testid="form-header">
            <h2 className="text-xl font-semibold mb-4">
              📅 Create New Appointment
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              Fill out all fields below to book a new appointment
            </p>
          </div>

          <div className="form-group" data-testid="treatment-form-group">
            <label
              htmlFor="treatment-dropdown"
              className="block text-sm font-medium mb-1"
              data-testid="treatment-label"
            >
              🛍️ Treatment: <span className="text-red-500">*</span>
            </label>
            <select
              id="treatment-dropdown"
              name="treatment"
              value={selectedTreatment}
              onChange={handleTreatmentChange}
              aria-label="Select a treatment for the appointment"
              data-testid="treatment-select-dropdown"
              className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="" data-testid="treatment-placeholder-option">
                Choose a treatment...
              </option>
              {actualTreatments.map((treatment, index) => (
                <option
                  key={treatment.id}
                  value={treatment.id}
                  data-testid={`treatment-option-${index}`}
                  data-treatment-name={treatment.name}
                >
                  {treatment.name} (${treatment.price},{' '}
                  {treatment.durationMinutes} min)
                </option>
              ))}
            </select>
            {errors.treatment && (
              <div
                className="error text-red-500 text-sm mt-1"
                data-testid="treatment-error-message"
                role="alert"
                aria-live="polite"
              >
                ❌ {errors.treatment}
              </div>
            )}
          </div>

          <div className="form-group" data-testid="staff-form-group">
            <label
              htmlFor="staff-dropdown"
              className="block text-sm font-medium mb-1"
              data-testid="staff-label"
            >
              👤 Staff Member: <span className="text-red-500">*</span>
            </label>
            <select
              id="staff-dropdown"
              name="staff"
              value={selectedStaff}
              onChange={handleStaffChange}
              aria-label="Select a staff member for the appointment"
              data-testid="staff-select-dropdown"
              className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="" data-testid="staff-placeholder-option">
                Choose a staff member...
              </option>
              {actualStaff.map((staff, index) => (
                <option
                  key={staff.id}
                  value={staff.id}
                  data-testid={`staff-option-${index}`}
                  data-staff-name={staff.name}
                >
                  {staff.name}
                </option>
              ))}
            </select>
            {errors.staff && (
              <div
                className="error text-red-500 text-sm mt-1"
                data-testid="staff-error-message"
                role="alert"
                aria-live="polite"
              >
                ❌ {errors.staff}
              </div>
            )}
          </div>

          <div className="form-group" data-testid="timeslot-form-group">
            <label
              htmlFor="timeslot-dropdown"
              className="block text-sm font-medium mb-1"
              data-testid="timeslot-label"
            >
              🕐 Time Slot: <span className="text-red-500">*</span>
            </label>
            <select
              id="timeslot-dropdown"
              name="timeSlot"
              value={selectedTimeSlot}
              onChange={handleTimeSlotChange}
              aria-label="Select a time slot for the appointment"
              data-testid="timeslot-select-dropdown"
              className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            >
              <option value="" data-testid="timeslot-placeholder-option">
                Choose a time slot...
              </option>
              {mockTimeSlots.map((slot, index) => {
                const startTime = new Date(slot.start).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                });
                const endTime = new Date(slot.end).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                });
                const isBooked = mockBookedSlots.some(
                  (bookedSlot) => bookedSlot.id === slot.id,
                );
                return (
                  <option
                    key={slot.id}
                    value={slot.id}
                    disabled={isBooked}
                    data-testid={`timeslot-option-${index}`}
                    data-time-range={`${startTime}-${endTime}`}
                    data-is-booked={isBooked}
                  >
                    {startTime} - {endTime}{' '}
                    {isBooked ? '(🚫 Booked)' : '(✅ Available)'}
                  </option>
                );
              })}
            </select>
            {errors.timeSlot && (
              <div
                className="error text-red-500 text-sm mt-1"
                data-testid="timeslot-error-message"
                role="alert"
                aria-live="polite"
              >
                ❌ {errors.timeSlot}
              </div>
            )}
          </div>

          <div
            className="client-details-section"
            data-testid="client-details-section"
          >
            <h3 className="text-lg font-medium mb-3 text-gray-800">
              👥 Client Information
            </h3>

            <div className="form-group" data-testid="client-name-form-group">
              <label
                htmlFor="client-name-input"
                className="block text-sm font-medium mb-1"
                data-testid="client-name-label"
              >
                📝 Client Name: <span className="text-red-500">*</span>
              </label>
              <input
                id="client-name-input"
                name="name"
                type="text"
                value={clientDetails.name}
                onChange={handleClientDetailsChange}
                aria-label="Enter the client's full name"
                data-testid="client-name-input-field"
                placeholder="Enter client's full name..."
                className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
              {errors.name && (
                <div
                  className="error text-red-500 text-sm mt-1"
                  data-testid="client-name-error-message"
                  role="alert"
                  aria-live="polite"
                >
                  ❌ {errors.name}
                </div>
              )}
            </div>

            <div className="form-group" data-testid="client-phone-form-group">
              <label
                htmlFor="client-phone-input"
                className="block text-sm font-medium mb-1"
                data-testid="client-phone-label"
              >
                📞 Phone Number: <span className="text-red-500">*</span>
              </label>
              <input
                id="client-phone-input"
                name="phone"
                type="tel"
                value={clientDetails.phone}
                onChange={handleClientDetailsChange}
                aria-label="Enter the client's 10-digit phone number"
                data-testid="client-phone-input-field"
                placeholder="1234567890 (10 digits only)"
                className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                pattern="[0-9]{10}"
                maxLength={10}
                required
              />
              {errors.phone && (
                <div
                  className="error text-red-500 text-sm mt-1"
                  data-testid="client-phone-error-message"
                  role="alert"
                  aria-live="polite"
                >
                  ❌ {errors.phone}
                </div>
              )}
            </div>

            <div className="form-group" data-testid="client-email-form-group">
              <label
                htmlFor="client-email-input"
                className="block text-sm font-medium mb-1"
                data-testid="client-email-label"
              >
                📧 Email Address: <span className="text-red-500">*</span>
              </label>
              <input
                id="client-email-input"
                name="email"
                type="email"
                value={clientDetails.email}
                onChange={handleClientDetailsChange}
                aria-label="Enter the client's email address"
                data-testid="client-email-input-field"
                placeholder="<EMAIL>"
                className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
              {errors.email && (
                <div
                  className="error text-red-500 text-sm mt-1"
                  data-testid="client-email-error-message"
                  role="alert"
                  aria-live="polite"
                >
                  ❌ {errors.email}
                </div>
              )}
            </div>
          </div>

          <div
            className="form-actions flex space-x-3 pt-6 border-t border-gray-200"
            data-testid="form-actions-section"
          >
            <button
              type="button"
              onClick={handleConfirm}
              data-testid="confirm-appointment-button"
              aria-label="Confirm and create the appointment"
              className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded flex-1 font-medium transition-colors"
            >
              ✅ Confirm Appointment
            </button>
            <button
              type="button"
              onClick={onCancel}
              data-testid="cancel-appointment-button"
              aria-label="Cancel and close the form"
              className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded flex-1 font-medium transition-colors"
            >
              ❌ Cancel
            </button>
          </div>

          {confirmationMessage && (
            <div
              className="confirmation bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mt-4"
              data-testid="appointment-confirmation-message"
              role="alert"
              aria-live="polite"
            >
              🎉 {confirmationMessage}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
