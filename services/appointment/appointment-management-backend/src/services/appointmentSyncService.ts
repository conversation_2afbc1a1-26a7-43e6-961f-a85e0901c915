import {
  EventSubscriber,
  StreamConfigs,
  SubscriberConfigs,
} from '@beauty-crm/platform-eventing';
import type { Appointment } from '@beauty-crm/platform-appointment-unified';
import { PrismaClient } from '@prisma/client';

export class AppointmentSyncService {
  private subscriber: EventSubscriber;
  private prisma: PrismaClient;

  constructor() {
    this.subscriber = new EventSubscriber(
      SubscriberConfigs.appointment('management-service')
    );
    this.prisma = new PrismaClient();
    this.subscriber
      .connect()
      .then(() => {
        console.log('NATS Subscriber Connected');
        this.subscribeToAppointmentEvents();
      })
      .catch(console.error);
  }

  private subscribeToAppointmentEvents(): void {
    this.subscriber.subscribe(
      StreamConfigs.appointments.name,
      EventChannels.APPOINTMENT_EVENTS,
      (err, msg) => {
        if (err) {
          console.error(
            `NATS Subscription Error on ${EventChannels.APPOINTMENT_EVENTS}:`,
            err
          );
          return;
        }
        (async () => {
          try {
            const event: AppointmentEvent = JSON.parse(msg.data.toString());
            console.log(`📨 Management System received: ${event.eventType}`);
            const appointment = UnifiedAppointmentSchema.parse(event.data);
            await this.handleAppointmentSync(appointment, event.eventType);
            msg.ack();
          } catch (error) {
            console.error('Error processing NATS message:', error);
            // For transient errors, negative acknowledge to retry
            // In a real system, implement exponential backoff and max retries
            msg.nak();
          }
        })();
      }
    );
  }

  private async checkForConflicts(
    appointment: UnifiedAppointment
  ): Promise<boolean> {
    const conflictingAppointments = await this.prisma.appointment.findMany({
      where: {
        NOT: { id: appointment.id },
        OR: [
          {
            endTime: { gte: appointment.startTime },
            startTime: { lte: appointment.endTime },
          },
        ], // Exclude the current appointment if it's an update
        staffId: appointment.staffId,
      },
    });

    if (conflictingAppointments.length > 0) {
      console.warn(
        `⚠️ Conflict detected for appointment ${appointment.id} with staff ${appointment.staffId}:`
      );
      for (const conflict of conflictingAppointments) {
        console.warn(
          `   - Conflicting appointment: ${conflict.id} from ${conflict.startTime} to ${conflict.endTime}`
        );
      }
      return true;
    }
    return false;
  }

  private async handleAppointmentSync(
    appointment: UnifiedAppointment,
    eventType: string
  ): Promise<void> {
    console.log(`   → Appointment ID: ${appointment.id}`);
    console.log(`   → Customer: ${appointment.customerId}`);
    console.log(`   → Treatment: ${appointment.treatmentName}`);
    console.log(`   → Start Time: ${appointment.startTime.toLocaleString()}`);

    try {
      const isConflict = await this.checkForConflicts(appointment);
      if (isConflict) {
        console.error(
          `❌ Conflict detected for appointment ${appointment.id}. Not processing.`
        );
        return;
      }

      switch (eventType) {
        case 'appointment.created':
          await this.prisma.appointment.create({
            data: {
              customerEmail: appointment.customerEmail,
              customerId: appointment.customerId,
              customerName: appointment.customerName,
              customerPhone: appointment.customerPhone,
              endTime: appointment.endTime,
              id: appointment.id,
              salonId: appointment.salonId,
              staffId: appointment.staffId || null,
              startTime: appointment.startTime,
              status: toPrismaStatus(appointment.status),
              treatmentId: appointment.treatmentId,
            },
          });
          console.log(
            '✅ Management System: Appointment synced successfully (created in DB)'
          );
          break;
        case 'appointment.updated':
          await this.prisma.appointment.update({
            data: {
              customerEmail: appointment.customerEmail,
              customerName: appointment.customerName,
              customerPhone: appointment.customerPhone,
              endTime: appointment.endTime,
              notes: appointment.notes,
              staffId: appointment.staffId || null,
              startTime: appointment.startTime,
              status: toPrismaStatus(appointment.status),
              treatmentId: appointment.treatmentId,
              treatmentName: appointment.treatmentName,
            },
            where: { id: appointment.id },
          });
          console.log(
            '✅ Management System: Appointment synced successfully (updated in DB)'
          );
          break;
        case 'appointment.cancelled':
          await this.prisma.appointment.update({
            data: { status: 'CANCELLED' },
            where: { id: appointment.id },
          });
          console.log(
            '✅ Management System: Appointment synced successfully (cancelled in DB)'
          );
          break;
        default:
          console.warn(`Unknown appointment event type: ${eventType}`);
      }
    } catch (error) {
      console.error('Error syncing appointment to database:', error);
    }
  }

  public async disconnect(): Promise<void> {
    await this.subscriber.disconnect();
    await this.prisma.$disconnect();
  }
}
