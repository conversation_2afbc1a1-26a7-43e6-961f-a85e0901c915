import type { Context } from 'hono';
import { z } from 'zod';
import type { AppointmentService } from '../application/services/AppointmentService';

// ✨ Using Single Source of Truth schema library
import {
  validateCreateRequest,
  validateUpdateRequest,
  CreateAppointmentSchema,
  UpdateAppointmentSchema,
  AppointmentStatusSchema,
} from '@beauty-crm/platform-appointment-unified';
import type {
  Appointment,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
} from '@beauty-crm/platform-appointment-unified';

// CUID validation regex pattern
const CUID_REGEX = /^c[a-z0-9]{24}$/;

// ✨ Use schema library validation instead of duplicate schemas
const appointmentSchema = CreateAppointmentSchema.extend({
  customerId: z
    .string()
    .regex(CUID_REGEX, 'Customer ID must be a valid CUID format'),
  staffId: z.string().regex(CUID_REGEX, 'Staff ID must be a valid CUID format'),
  treatmentId: z
    .string()
    .regex(CUID_REGEX, 'Treatment ID must be a valid CUID format'),
});

export class AppointmentController {
  constructor(private appointmentService: AppointmentService) {}

  // Get all appointments
  getAll = async (c: Context) => {
    try {
      const appointments = await this.appointmentService.getAllAppointments();
      return c.json({
        data: appointments.map((a) => a.toJSON()),
        success: true,
      });
    } catch (error) {
      console.error('Error getting appointments:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500
      );
    }
  };

  // Get appointments by date range
  getByDateRange = async (c: Context) => {
    try {
      const { startDate, endDate } = c.req.query();

      if (!startDate || !endDate) {
        return c.json(
          {
            error: 'Start date and end date are required',
            success: false,
          },
          400
        );
      }

      const appointments = await this.appointmentService.getAppointmentsByDateRange(
        new Date(startDate),
        new Date(endDate)
      );

      return c.json({
        data: appointments.map((a) => a.toJSON()),
        success: true,
      });
    } catch (error) {
      console.error('Error getting appointments by date range:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500
      );
    }
  };

  // Get appointments by staff and date range
  getByStaffAndDateRange = async (c: Context) => {
    try {
      const { staffId } = c.req.param();
      const { startDate, endDate } = c.req.query();

      if (!staffId || !startDate || !endDate) {
        return c.json(
          {
            error: 'Staff ID, start date, and end date are required',
            success: false,
          },
          400
        );
      }

      // Validate staffId is a proper CUID
      if (!CUID_REGEX.test(staffId)) {
        return c.json(
          {
            error: 'Staff ID must be a valid CUID format',
            success: false,
          },
          400
        );
      }

      const appointments = await this.appointmentService.getAppointmentsByStaffAndDateRange(
        staffId,
        new Date(startDate),
        new Date(endDate)
      );

      return c.json({
        data: appointments.map((a) => a.toJSON()),
        success: true,
      });
    } catch (error) {
      console.error(
        'Error getting appointments by staff and date range:',
        error
      );
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500
      );
    }
  };

  // Get appointment by ID
  getById = async (c: Context) => {
    try {
      const { id } = c.req.param();
      const appointment = await this.appointmentService.getAppointmentById(id);

      if (!appointment) {
        return c.json({ error: 'Appointment not found', success: false }, 404);
      }

      return c.json({ data: appointment.toJSON(), success: true });
    } catch (error) {
      console.error('Error getting appointment by ID:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500
      );
    }
  };

  // Create a new appointment
  create = async (c: Context) => {
    try {
      const body = await c.req.json();

      // Validate input
      const validationResult = appointmentSchema.safeParse(body);
      if (!validationResult.success) {
        return c.json(
          {
            details: validationResult.error.errors,
            error: 'Invalid appointment data',
            success: false,
          },
          400
        );
      }

      // ✨ Use schema library validation
      const appointmentData = validateCreateRequest(validationResult.data);
      const appointment = await this.appointmentService.createAppointment(
        appointmentData
      );

      return c.json({ data: appointment, success: true }, 201);
    } catch (error) {
      console.error('Error creating appointment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500
      );
    }
  };

  // Update an appointment
  update = async (c: Context) => {
    try {
      const { id } = c.req.param();
      const body = await c.req.json();

      const appointment = await this.appointmentService.updateAppointment(
        id,
        body
      );

      if (!appointment) {
        return c.json({ error: 'Appointment not found', success: false }, 404);
      }

      return c.json({ data: appointment.toJSON(), success: true });
    } catch (error) {
      console.error('Error updating appointment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500
      );
    }
  };

  // Cancel an appointment
  cancel = async (c: Context) => {
    try {
      const { id } = c.req.param();
      const appointment = await this.appointmentService.cancelAppointment(id);

      if (!appointment) {
        return c.json({ error: 'Appointment not found', success: false }, 404);
      }

      return c.json({ data: appointment.toJSON(), success: true });
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500
      );
    }
  };

  // Delete an appointment
  delete = async (c: Context) => {
    try {
      const { id } = c.req.param();
      await this.appointmentService.deleteAppointment(id);

      return c.json({
        message: 'Appointment deleted successfully',
        success: true,
      });
    } catch (error) {
      console.error('Error deleting appointment:', error);
      return c.json(
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false,
        },
        500
      );
    }
  };
}
