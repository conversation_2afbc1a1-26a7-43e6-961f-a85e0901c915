import type {
  Appointment,
  CreateAppointmentRequest as AppointmentProps,
} from '@beauty-crm/platform-appointment-unified';
import type { AppointmentRepository } from '@beauty-crm/platform-appointment-unified';
import type { TreatmentCacheService } from '../../infrastructure/services/TreatmentCacheService';

export class AppointmentService {
  constructor(
    private appointmentRepository: AppointmentRepository,
    private treatmentCacheService?: TreatmentCacheService
  ) {}

  async getAllAppointments(): Promise<Appointment[]> {
    // For demonstration - in real implementation would have pagination
    return this.appointmentRepository.findAll();
  }

  async getAppointmentById(id: string): Promise<Appointment | null> {
    return this.appointmentRepository.findById(id);
  }

  async getAppointmentsByCustomerId(
    customerId: string
  ): Promise<Appointment[]> {
    return this.appointmentRepository.findByCustomerId(customerId);
  }

  async getAppointmentsByStaffId(staffId: string): Promise<Appointment[]> {
    return this.appointmentRepository.findByStaffId(staffId);
  }

  async getAppointmentsByDateRange(
    _startDate: Date,
    _endDate: Date
  ): Promise<Appointment[]> {
    // Simplified implementation - would need more sophisticated query
    return this.appointmentRepository.findByStaffId('');
  }

  async getAppointmentsByStaffAndDateRange(
    staffId: string,
    _startDate: Date,
    _endDate: Date
  ): Promise<Appointment[]> {
    // Simplified implementation
    return this.appointmentRepository.findByStaffId(staffId);
  }

  async createAppointment(
    appointmentData: AppointmentProps
  ): Promise<Appointment> {
    // Check for overlapping appointments
    const { staffId, startTime, endTime } = appointmentData;
    const startDate =
      startTime instanceof Date ? startTime : new Date(startTime);
    const endDate = endTime instanceof Date ? endTime : new Date(endTime);

    const overlappingAppointments = await this.appointmentRepository.findConflictingAppointments(
      staffId,
      startDate,
      endDate
    );

    if (overlappingAppointments.length > 0) {
      throw new Error(
        'The staff member already has an appointment during this time slot'
      );
    }

    const appointment = new Appointment(appointmentData);
    const created = await this.appointmentRepository.create(appointment);
    return created;
  }

  async updateAppointment(
    id: string,
    updateData: Partial<AppointmentProps>
  ): Promise<Appointment> {
    const existingAppointment = await this.appointmentRepository.findById(id);
    if (!existingAppointment) {
      throw new Error('Appointment not found');
    }

    // Update appointment properties
    Object.assign(existingAppointment, updateData);
    existingAppointment.updatedAt = new Date();

    return this.appointmentRepository.update(existingAppointment);
  }

  async deleteAppointment(id: string): Promise<void> {
    return this.appointmentRepository.delete(id);
  }

  async createFromExternal(
    appointmentData: AppointmentProps
  ): Promise<Appointment> {
    const appointment = new Appointment(appointmentData);
    return this.appointmentRepository.create(appointment);
  }

  async updateFromExternal(
    appointmentData: Partial<AppointmentProps>
  ): Promise<Appointment> {
    const existingAppointment = await this.appointmentRepository.findById(
      appointmentData.id as string
    );
    if (!existingAppointment) {
      throw new Error('Appointment not found');
    }
    Object.assign(existingAppointment, appointmentData);
    existingAppointment.updatedAt = new Date();
    return this.appointmentRepository.update(existingAppointment);
  }

  async cancelFromExternal(
    appointmentData: Partial<AppointmentProps>
  ): Promise<Appointment> {
    const existingAppointment = await this.appointmentRepository.findById(
      appointmentData.id as string
    );
    if (!existingAppointment) {
      throw new Error('Appointment not found');
    }
    existingAppointment.cancel();
    existingAppointment.updatedAt = new Date();
    return this.appointmentRepository.update(existingAppointment);
  }
}
