import type { Appointment } from '@beauty-crm/platform-appointment-unified';

export interface AppointmentRepository {
  create(appointment: Appointment): Promise<Appointment>;
  findById(id: string): Promise<Appointment | null>;
  findAll(): Promise<Appointment[]>;
  findByStaffId(staffId: string): Promise<Appointment[]>;
  findByCustomerId(customerId: string): Promise<Appointment[]>;
  update(appointment: Appointment): Promise<Appointment>;
  delete(id: string): Promise<void>;
  findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date,
  ): Promise<Appointment[]>;
}
