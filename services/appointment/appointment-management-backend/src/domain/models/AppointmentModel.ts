/**
 * 🎯 APPOINTMENT MODEL - Domain Layer
 *
 * Uses shared schema library as single source of truth
 * Enhanced with class-validator decorators for runtime validation
 */

import 'reflect-metadata';
import {
  IsString,
  IsEmail,
  IsOptional,
  IsDate,
  IsEnum,
  IsNumber,
  IsPositive,
  IsInt,
  Min,
  IsUrl,
  validateSync,
  type ValidationError,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import {
  type Appointment as SharedAppointment,
  type AppointmentStatus as SharedAppointmentStatus,
  type CreateAppointmentRequest,
  AppointmentStatusSchema,
  validateAppointment,
  validateCreateRequest,
} from '@beauty-crm/platform-appointment-unified';

// ============================================================================
// 🔥 DOMAIN MODEL - Enhanced with Validation Decorators
// ============================================================================

export class Appointment implements SharedAppointment {
  @IsString()
  id!: string;

  @IsString()
  salonId!: string;

  @IsString()
  customerId!: string;

  @IsOptional()
  @IsString()
  staffId?: string;

  @IsString()
  treatmentId!: string;

  // Customer Info (denormalized)
  @IsString()
  @Min(1)
  customerName!: string;

  @IsEmail()
  customerEmail!: string;

  @IsOptional()
  @IsString()
  customerPhone?: string;

  // Treatment Info (denormalized)
  @IsString()
  @Min(1)
  treatmentName!: string;

  @IsInt()
  @IsPositive()
  treatmentDuration!: number;

  @IsNumber()
  @IsPositive()
  treatmentPrice!: number;

  // Salon Info (denormalized)
  @IsString()
  @Min(1)
  salonName!: string;

  @IsOptional()
  @IsUrl()
  salonLogo?: string;

  @IsOptional()
  @IsString()
  salonColor?: string;

  // Scheduling
  @IsDate()
  @Type(() => Date)
  startTime!: Date;

  @IsDate()
  @Type(() => Date)
  endTime!: Date;

  // Status & Metadata
  @IsEnum([
    'PENDING',
    'CONFIRMED',
    'SCHEDULED',
    'IN_PROGRESS',
    'COMPLETED',
    'CANCELLED',
    'NO_SHOW',
    'RESCHEDULED',
  ])
  status!: SharedAppointmentStatus;

  @IsOptional()
  @IsString()
  notes?: string;

  @IsString()
  @Transform(({ value }) => value || 'en-US')
  locale!: string;

  // Source tracking
  @IsEnum(['planner', 'management', 'api', 'import'])
  @Transform(({ value }) => value || 'planner')
  source!: 'planner' | 'management' | 'api' | 'import';

  @IsOptional()
  @IsString()
  plannerAppointmentId?: string;

  // Timestamps
  @IsDate()
  @Type(() => Date)
  createdAt!: Date;

  @IsDate()
  @Type(() => Date)
  updatedAt!: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  confirmedAt?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  completedAt?: Date;

  @IsOptional()
  @IsDate()
  @Type(() => Date)
  cancelledAt?: Date;

  // ============================================================================
  // 🎯 BUSINESS LOGIC METHODS
  // ============================================================================

  /**
   * Validates the appointment using class-validator
   */
  validate(): ValidationError[] {
    return validateSync(this);
  }

  /**
   * Checks if the appointment is valid
   */
  isValid(): boolean {
    return this.validate().length === 0;
  }

  /**
   * Confirms the appointment
   */
  confirm(): void {
    if (this.status === 'CANCELLED') {
      throw new Error('Cannot confirm a cancelled appointment');
    }
    this.status = 'CONFIRMED';
    this.confirmedAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Cancels the appointment
   */
  cancel(reason?: string): void {
    if (this.status === 'COMPLETED') {
      throw new Error('Cannot cancel a completed appointment');
    }
    this.status = 'CANCELLED';
    this.cancelledAt = new Date();
    this.updatedAt = new Date();
    if (reason) {
      this.notes = this.notes
        ? `${this.notes}\nCancellation reason: ${reason}`
        : `Cancellation reason: ${reason}`;
    }
  }

  /**
   * Completes the appointment
   */
  complete(): void {
    if (this.status !== 'IN_PROGRESS' && this.status !== 'CONFIRMED') {
      throw new Error(
        'Can only complete appointments that are in progress or confirmed',
      );
    }
    this.status = 'COMPLETED';
    this.completedAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Checks if appointment is in the past
   */
  isPast(): boolean {
    return this.endTime < new Date();
  }

  /**
   * Checks if appointment is upcoming
   */
  isUpcoming(): boolean {
    return this.startTime > new Date();
  }

  /**
   * Gets appointment duration in minutes
   */
  getDurationMinutes(): number {
    return Math.round(
      (this.endTime.getTime() - this.startTime.getTime()) / (1000 * 60),
    );
  }

  /**
   * Converts to plain object (compatible with shared schema)
   */
  toPlainObject(): SharedAppointment {
    return {
      id: this.id,
      salonId: this.salonId,
      customerId: this.customerId,
      staffId: this.staffId,
      treatmentId: this.treatmentId,
      customerName: this.customerName,
      customerEmail: this.customerEmail,
      customerPhone: this.customerPhone,
      treatmentName: this.treatmentName,
      treatmentDuration: this.treatmentDuration,
      treatmentPrice: this.treatmentPrice,
      salonName: this.salonName,
      salonLogo: this.salonLogo,
      salonColor: this.salonColor,
      startTime: this.startTime,
      endTime: this.endTime,
      status: this.status,
      notes: this.notes,
      locale: this.locale,
      source: this.source,
      plannerAppointmentId: this.plannerAppointmentId,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      confirmedAt: this.confirmedAt,
      completedAt: this.completedAt,
      cancelledAt: this.cancelledAt,
    };
  }
}

// ============================================================================
// 🎯 FACTORY FUNCTIONS
// ============================================================================

/**
 * Creates an Appointment from shared schema data
 */
export function createAppointmentFromShared(
  data: SharedAppointment,
): Appointment {
  const appointment = new Appointment();
  Object.assign(appointment, data);
  return appointment;
}

/**
 * Creates an Appointment from create request
 */
export function createAppointmentFromRequest(
  data: CreateAppointmentRequest,
): Appointment {
  // Validate using shared schema first
  const validatedData = validateCreateRequest(data);

  const appointment = new Appointment();
  Object.assign(appointment, {
    ...validatedData,
    id: '', // Will be set by repository
    createdAt: new Date(),
    updatedAt: new Date(),
    status: 'PENDING' as const,
  });

  return appointment;
}

// ============================================================================
// 🎯 TYPE EXPORTS
// ============================================================================

export type AppointmentStatus = SharedAppointmentStatus;
export type AppointmentProps = CreateAppointmentRequest;

// Re-export shared types for convenience
export type {
  Appointment as SharedAppointment,
  CreateAppointmentRequest,
  UpdateAppointmentRequest,
  AppointmentResponse,
} from '@beauty-crm/platform-appointment-unified';
