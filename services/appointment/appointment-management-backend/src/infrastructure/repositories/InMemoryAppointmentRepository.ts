import { v4 as uuidv4 } from 'uuid';
import type { Appointment } from '@beauty-crm/platform-appointment-unified';
import type { AppointmentRepository } from '../../domain/repositories/appointment-repository';

export class InMemoryAppointmentRepository implements AppointmentRepository {
  private appointments: Appointment[] = [];

  constructor(initialAppointments: Appointment[] = []) {
    this.appointments = initialAppointments;
  }

  async create(appointment: Appointment): Promise<Appointment> {
    const newAppointment: Appointment = {
      createdAt: new Date(),
      customerEmail: appointment.customerEmail,
      customerId: appointment.customerId,
      customerName: appointment.customerName,
      customerPhone: appointment.customerPhone,
      endTime: appointment.endTime,
      id: appointment.id || uuidv4(),
      notes: appointment.notes,
      salonId: appointment.salonId,
      staffId: appointment.staffId,
      startTime: appointment.startTime,
      status: appointment.status,
      treatmentId: appointment.treatmentId,
      updatedAt: new Date(),
    };
    this.appointments.push(newAppointment);
    return newAppointment;
  }

  async findAll(): Promise<Appointment[]> {
    return [...this.appointments].sort(
      (a, b) => a.startTime.getTime() - b.startTime.getTime()
    );
  }

  async findById(id: string): Promise<Appointment | null> {
    const appointment = this.appointments.find((a) => a.id === id);
    return appointment || null;
  }

  async findByCustomerId(customerId: string): Promise<Appointment[]> {
    return this.appointments.filter((a) => a.customerId === customerId);
  }

  async findByStaffId(staffId: string): Promise<Appointment[]> {
    return this.appointments.filter((a) => a.staffId === staffId);
  }

  async findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date
  ): Promise<Appointment[]> {
    return this.appointments.filter(
      (a) =>
        a.staffId === staffId &&
        ((startTime >= a.startTime && startTime < a.endTime) ||
          (endTime > a.startTime && endTime <= a.endTime) ||
          (startTime <= a.startTime && endTime >= a.endTime))
    );
  }

  async update(appointment: Appointment): Promise<Appointment> {
    const index = this.appointments.findIndex((a) => a.id === appointment.id);
    if (index === -1) {
      throw new Error(`Appointment with id ${appointment.id} not found`);
    }

    const updatedAppointment: Appointment = {
      createdAt: appointment.createdAt,
      customerEmail: appointment.customerEmail,
      customerId: appointment.customerId,
      customerName: appointment.customerName,
      customerPhone: appointment.customerPhone,
      endTime: appointment.endTime,
      id: appointment.id,
      notes: appointment.notes,
      salonId: appointment.salonId,
      staffId: appointment.staffId,
      startTime: appointment.startTime,
      status: appointment.status,
      treatmentId: appointment.treatmentId,
      updatedAt: new Date(),
    };

    this.appointments[index] = updatedAppointment;
    return updatedAppointment;
  }

  async delete(id: string): Promise<void> {
    const index = this.appointments.findIndex((a) => a.id === id);
    if (index === -1) {
      throw new Error(`Appointment with id ${id} not found`);
    }
    this.appointments.splice(index, 1);
  }
}
