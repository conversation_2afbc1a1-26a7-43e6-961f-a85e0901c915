import {
  type AppointmentStatus,
} from '@beauty-crm/platform-appointment-unified';
import type { PrismaClient } from '@prisma/client';
import type {
  Appointment,
} from '@beauty-crm/platform-appointment-unified';
import type { AppointmentRepository } from '../../domain/repositories/appointment-repository';

// Convert appointment status to Prisma status string
function toPrismaStatus(status: AppointmentStatus): string {
  return status; // Direct mapping since our unified library uses string enums
}

// Convert Prisma status string to appointment status
function fromPrismaStatus(status: string): AppointmentStatus {
  return status as AppointmentStatus; // Direct mapping since our unified library uses string enums
}

// Local fromPrismaStatus function since import is not working
function fromPrismaStatus(status: string): UnifiedAppointmentStatus {
  switch (status) {
    case 'PENDING':
      return UnifiedAppointmentStatus.PENDING;
    case 'CONFIRMED':
      return UnifiedAppointmentStatus.CONFIRMED;
    case 'CANCELLED':
      return UnifiedAppointmentStatus.CANCELLED;
    case 'COMPLETED':
      return UnifiedAppointmentStatus.COMPLETED;
    case 'RESCHEDULED':
      return UnifiedAppointmentStatus.RESCHEDULED;
    default:
      return UnifiedAppointmentStatus.PENDING;
  }
}

// Helper function to convert Prisma appointment to domain appointment
function prismaToAppointment(prismaAppointment: any): Appointment {
  return {
    id: prismaAppointment.id,
    salonId: prismaAppointment.salonId,
    customerId: prismaAppointment.customerId,
    treatmentId: prismaAppointment.treatmentId,
    customerName: prismaAppointment.customerName || '',
    customerEmail: prismaAppointment.customerEmail || '',
    treatmentName: '', // Will be populated by service layer
    treatmentDuration: 0, // Will be populated by service layer
    treatmentPrice: 0, // Will be populated by service layer
    salonName: '', // Will be populated by service layer
    staffId: prismaAppointment.staffId || '',
    staffName: '', // Will be populated by service layer
    customerPhone: prismaAppointment.customerPhone || '',
    startTime: prismaAppointment.startTime,
    endTime: prismaAppointment.endTime,
    status: fromPrismaStatus(prismaAppointment.status),
    notes: prismaAppointment.notes || '',
    createdAt: prismaAppointment.createdAt,
    updatedAt: prismaAppointment.updatedAt,
    source: 'management',
    plannerAppointmentId: prismaAppointment.plannerAppointmentId,
  };
}

export class PrismaAppointmentRepository implements AppointmentRepository {
  constructor(private prisma: PrismaClient) {}

  async create(appointment: Appointment): Promise<Appointment> {
    const created = await this.prisma.appointment.create({
      data: {
        customerId: appointment.customerId,
        endTime: appointment.endTime,
        notes: appointment.notes || undefined,
        salonId: appointment.salonId,
        staffId: appointment.staffId || undefined,
        startTime: appointment.startTime,
        status: toPrismaStatus(appointment.status),
        treatmentId: appointment.treatmentId,
        customerName: appointment.customerName,
        customerEmail: appointment.customerEmail,
        customerPhone: appointment.customerPhone,
      },
    });

    return prismaToAppointment(created);
  }

  async findById(id: string): Promise<Appointment | null> {
    const appointment = await this.prisma.appointment.findUnique({
      where: { id },
    });

    if (!appointment) return null;

    return prismaToAppointment(appointment);
  }

  async findAll(): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      orderBy: { startTime: 'asc' },
    });

    return appointments.map(prismaToAppointment);
  }

  async findByStaffId(staffId: string): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: { staffId },
    });

    return appointments.map(prismaToAppointment);
  }

  async findByCustomerId(customerId: string): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: { customerId },
    });

    return appointments.map(prismaToAppointment);
  }

  async update(appointment: Appointment): Promise<Appointment> {
    const updated = await this.prisma.appointment.update({
      data: {
        customerEmail: appointment.customerEmail,
        customerId: appointment.customerId,
        customerName: appointment.customerName,
        customerPhone: appointment.customerPhone,
        endTime: appointment.endTime,
        notes: appointment.notes,
        salonId: appointment.salonId,
        staffId: appointment.staffId,
        startTime: appointment.startTime,
        status: toPrismaStatus(domainStatusToUnified(appointment.status)),
        treatmentId: appointment.treatmentId,
      },
      where: { id: appointment.id },
    });

    return prismaToAppointment(updated);
  }

  async delete(id: string): Promise<void> {
    await this.prisma.appointment.delete({
      where: { id },
    });
  }

  async findConflictingAppointments(
    staffId: string,
    startTime: Date,
    endTime: Date,
  ): Promise<Appointment[]> {
    const appointments = await this.prisma.appointment.findMany({
      where: {
        staffId,
        OR: [
          {
            AND: [
              { startTime: { lte: startTime } },
              { endTime: { gt: startTime } },
            ],
          },
          {
            AND: [
              { startTime: { lt: endTime } },
              { endTime: { gte: endTime } },
            ],
          },
          {
            AND: [
              { startTime: { gte: startTime } },
              { endTime: { lte: endTime } },
            ],
          },
        ],
      },
    });

    return appointments.map(prismaToAppointment);
  }
}
