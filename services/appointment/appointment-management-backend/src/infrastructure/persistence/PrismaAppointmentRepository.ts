import type { Appointment } from '@beauty-crm/platform-appointment-unified';
import type { Prisma } from '@prisma/client';
import type { AppointmentRepository } from '../../domain/ports/AppointmentRepository';

/**
 * @class PrismaAppointmentRepository
 * @description A Prisma-based implementation of the AppointmentRepository for the management backend.
 * It handles all database operations for the Appointment aggregate and is
 * designed to work within a transactional context managed by an application service.
 */
export class PrismaAppointmentRepository implements AppointmentRepository {
  /**
   * Finds an appointment by its ID within a transaction.
   *
   * @param {string} id
   * @param {Prisma.TransactionClient} tx
   * @returns {Promise<UnifiedAppointment | null>}
   */
  public async findById(
    id: string,
    tx: Prisma.TransactionClient
  ): Promise<UnifiedAppointment | null> {
    const appointment = await tx.appointment.findUnique({
      where: { id },
    });

    // This is a placeholder for a proper mapping function
    return appointment as UnifiedAppointment | null;
  }

  /**
   * Creates a new appointment within a transaction.
   *
   * @param {UnifiedAppointment} appointment
   * @param {Prisma.TransactionClient} tx
   * @returns {Promise<void>}
   */
  public async create(
    appointment: UnifiedAppointment,
    tx: Prisma.TransactionClient
  ): Promise<void> {
    const data = toPrismaAppointment(appointment);
    await tx.appointment.create({ data });
  }

  /**
   * Updates an existing appointment within a transaction.
   *
   * @param {UnifiedAppointment} appointment
   * @param {Prisma.TransactionClient} tx
   * @returns {Promise<void>}
   */
  public async update(
    appointment: UnifiedAppointment,
    tx: Prisma.TransactionClient
  ): Promise<void> {
    const data = toPrismaAppointment(appointment);
    await tx.appointment.update({
      data,
      where: { id: appointment.id },
    });
  }
}
