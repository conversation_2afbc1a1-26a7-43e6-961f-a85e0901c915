{"dependencies": {"@beauty-crm/platform-computing-lifecycle": "workspace:*", "@beauty-crm/platform-environment-names": "workspace:*", "@beauty-crm/platform-identity-client": "workspace:*", "@beauty-crm/platform-introvertic-ui": "workspace:*", "@beauty-crm/platform-logger": "workspace:*", "@beauty-crm/platform-utilities": "workspace:*", "@beauty-crm/product-domain-types": "workspace:*", "@beauty-crm/product-identity-types": "workspace:*", "@beauty-crm/product-kernel": "workspace:*", "@hookform/resolvers": "^4.1.3", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "clsx": "^2.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.513.0", "msw": "^2.10.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-router-dom": "^7.6.3", "zod": "^3.25.73"}, "description": "Appointment Planner Frontend", "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "supertest": "^7.1.3", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.4"}, "name": "@beauty-crm/appointment-planner-frontend", "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"build": "vite build", "build:test": "tsc --project tsconfig.test.json", "dev": "vite --host 0.0.0.0", "dev:test": "vite --port ${FRONTEND_PORT:-5016} --host 0.0.0.0", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "vite preview", "test": "vitest run", "test:api": "vitest run src/tests/api/", "test:api:watch": "vitest watch src/tests/api/", "test:ci": "CI=true vitest run", "test:coverage": "vitest run --coverage", "test:coverage:api": "vitest run --coverage src/tests/api/", "test:coverage:html": "vitest run --coverage --reporter=html", "test:coverage:threshold": "vitest run --coverage --coverage.thresholds.lines=70 --coverage.thresholds.functions=70 --coverage.thresholds.branches=70 --coverage.thresholds.statements=70", "test:ui": "vitest --ui", "test:watch": "vitest"}, "type": "module", "version": "1.0.0"}