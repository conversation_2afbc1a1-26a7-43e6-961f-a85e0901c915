/**
 * 🏗️ Beautiful Event Builder
 *
 * Fluent API for creating domain events with elegance
 */
import type { DomainEvent, EventMetadata } from './types.ts';
/**
 * 🌟 Beautiful Event Builder
 *
 * Create domain events with a delightful fluent API
 */
export declare class EventBuilder<TData = unknown> {
  private event;
  /**
   * 🆔 Set the event ID (auto-generated if not provided)
   */
  id(eventId: string): this;
  /**
   * 🏷️ Set the event type
   */
  type(eventType: string): this;
  /**
   /**
    * 🎯 Set the aggregate information
    *
    * An aggregate represents a cluster of domain objects that can be treated as a single unit.
    * In domain-driven design, aggregates are the consistency boundaries for changes and transactions.
    * This method sets the aggregate's unique identifier and type for the event.
    */
  aggregate(aggregateId: string, aggregateType: string): this;
  /**
   * 📦 Set the event data
   */
  data(data: TData): this;
  /**
   * 🏢 Set the source service
   */
  source(source: string): this;
  /**
   * 📅 Set the timestamp (defaults to now)
   */
  timestamp(timestamp: string | Date): this;
  /**
   * 🔢 Set the event version
   */
  version(version: number): this;
  /**
   * 🔗 Set correlation ID for request tracing
   */
  correlationId(correlationId: string): this;
  /**
   * 🔗 Set causation ID linking to the causing event
   */
  causationId(causationId: string): this;
  /**
   * 👤 Set the user who triggered this event
   */
  userId(userId: string): this;
  /**
   * 🏷️ Add custom metadata
   */
  metadata(key: string, value: unknown): this;
  metadata(metadata: EventMetadata): this;
  /**
   * ✨ Build the beautiful domain event
   */
  build(): DomainEvent<TData>;
}
/**
 * 🏗️ Create a new event builder
 */
export declare function createEvent<TData = unknown>(): EventBuilder<TData>;
/**
 * 🎯 Create an event for a specific aggregate
 */
export declare function createEventFor<TData = unknown>(
  aggregateType: string,
  aggregateId: string
): EventBuilder<TData>;
/**
 * 📝 Create a domain event with common patterns
 */
export declare function createDomainEvent<TData = unknown>(options: {
  eventType: string;
  aggregateId: string;
  aggregateType: string;
  data: TData;
  source: string;
  correlationId?: string;
  userId?: string;
  metadata?: EventMetadata;
}): DomainEvent<TData>;
/**
 * 🏷️ Create event type with consistent naming
 */
export declare function eventType(aggregate: string, action: string): string;
/**
 * 📋 Common event types
 */
export declare const EventTypes: {
  readonly activated: (aggregate: string) => string;
  readonly archived: (aggregate: string) => string;
  readonly cancelled: (aggregate: string) => string;
  readonly completed: (aggregate: string) => string;
  readonly confirmed: (aggregate: string) => string;
  readonly created: (aggregate: string) => string;
  readonly deactivated: (aggregate: string) => string;
  readonly deleted: (aggregate: string) => string;
  readonly rescheduled: (aggregate: string) => string;
  readonly scheduled: (aggregate: string) => string;
  readonly synced: (aggregate: string) => string;
  readonly syncFailed: (aggregate: string) => string;
  readonly updated: (aggregate: string) => string;
};
/**
 * 🎯 Build NATS subject patterns
 */
export declare class SubjectBuilder {
  private parts;
  /**
   * 🏢 Add aggregate type
   */
  aggregate(type: string): this;
  /**
   * 📡 Add events namespace
   */
  events(): this;
  /**
   * 🏷️ Add event action
   */
  action(action: string): this;
  /**
   * 🆔 Add specific ID
   */
  id(id: string): this;
  /**
   * 🌟 Add wildcard
   */
  wildcard(): this;
  /**
   * ✨ Build the subject
   */
  build(): string;
}
/**
 * 🎯 Create a subject builder
 */
export declare function subject(): SubjectBuilder;
/**
 * 📋 Common subject patterns
 */
export declare const Subjects: {
  readonly aggregateEvents: (aggregate: string, id: string) => string;
  readonly allEvents: (aggregate: string) => string;
  readonly eventType: (aggregate: string, action: string) => string;
};
//# sourceMappingURL=EventBuilder.d.ts.map
