// Minimal Prisma schema for platform-db-client
// This schema is used only to generate the PrismaClient type
// Actual database schemas are defined in individual services

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "darwin-arm64", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Minimal model to ensure PrismaClient generation works
// This is not used in practice - services use their own schemas
model PlatformDbClientMeta {
  id        String   @id @default(cuid())
  version   String
  createdAt DateTime @default(now())

  @@map("platform_db_client_meta")
}
