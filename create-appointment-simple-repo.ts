/**
 * 🎯 SIMPLE APPOINTMENT CREATOR WITH REPOSITORY PATTERN
 *
 * This script creates an appointment using a simple repository pattern
 */

import { PrismaClient } from '@prisma/client';

/**
 * Database connection
 */
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/beauty_crm'
    }
  }
});

/**
 * Simple appointment data interface
 */
interface CreateAppointmentData {
  salonId: string;
  customerId: string;
  staffId?: string;
  treatmentId: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  treatmentName: string;
  treatmentDuration: number;
  treatmentPrice: number;
  startTime: Date;
  endTime: Date;
  status: string;
  notes?: string;
  locale: string;
  source: string;
}

/**
 * Simple appointment repository
 */
class SimpleAppointmentRepository {
  constructor(private prisma: PrismaClient) {}

  async create(data: CreateAppointmentData) {
    return await this.prisma.appointment.create({
      data: {
        salonId: data.salonId,
        customerId: data.customerId,
        staffId: data.staffId,
        treatmentId: data.treatmentId,
        customerName: data.customerName,
        customerEmail: data.customerEmail,
        customerPhone: data.customerPhone,
        treatmentName: data.treatmentName,
        treatmentDuration: data.treatmentDuration,
        treatmentPrice: data.treatmentPrice,
        startTime: data.startTime,
        endTime: data.endTime,
        status: data.status,
        notes: data.notes,
        locale: data.locale,
        source: data.source,
      },
    });
  }

  async findById(id: string) {
    return await this.prisma.appointment.findUnique({
      where: { id },
    });
  }

  async findAll() {
    return await this.prisma.appointment.findMany({
      orderBy: { startTime: 'asc' },
    });
  }
}

/**
 * Repository instance
 */
const appointmentRepository = new SimpleAppointmentRepository(prisma);

/**
 * Create a demo appointment using the repository
 */
async function createDemoAppointment() {
  console.log('🎯 Creating demo appointment with simple repository...');

  try {
    // Demo appointment data
    const appointmentData: CreateAppointmentData = {
      salonId: 'salon_demo_001',
      customerId: 'customer_demo_001',
      staffId: 'staff_demo_001',
      treatmentId: 'treatment_demo_001',
      customerName: 'John Doe',
      customerEmail: '<EMAIL>',
      customerPhone: '+1234567890',
      treatmentName: 'Haircut & Style',
      treatmentDuration: 60,
      treatmentPrice: 75.0,
      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      endTime: new Date(Date.now() + 25 * 60 * 60 * 1000), // Tomorrow + 1 hour
      status: 'PENDING',
      notes: 'Created via simple repository pattern',
      locale: 'en-US',
      source: 'simple-repo-demo',
    };

    console.log('📋 Creating appointment with data:', JSON.stringify(appointmentData, null, 2));

    // Create the appointment using the repository
    const createdAppointment = await appointmentRepository.create(appointmentData);
    
    console.log('✅ Appointment created successfully!');
    console.log('📋 Created appointment:', JSON.stringify(createdAppointment, null, 2));

    return createdAppointment;

  } catch (error) {
    console.error('❌ Error creating appointment:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    const appointment = await createDemoAppointment();
    console.log('🎉 Demo appointment created with ID:', appointment.id);
    process.exit(0);
  } catch (error) {
    console.error('💥 Failed to create demo appointment:', error);
    process.exit(1);
  }
}

// Run the script
main();
