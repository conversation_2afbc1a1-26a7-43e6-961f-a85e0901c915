/**
 * 🎯 CREATE APPOINTMENT VIA DATABASE - Direct Database Access
 *
 * This script creates an appointment directly in the database using our unified library
 * and exposes it via a simple HTTP endpoint that can be called with curl
 */

import { serve } from 'bun';
import { PrismaClient } from '@prisma/client';

// Import from our unified library using workspace path
import {
  type Appointment,
  type CreateAppointmentRequest,
  AppointmentEntity,
  type IAppointmentProps,
  AppointmentModel,
  SimpleAppointmentRepository,
} from '@beauty-crm/platform-appointment-unified';

/**
 * Database connection
 */
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/beauty_crm'
    }
  }
});

/**
 * Repository using our unified library
 */
const appointmentRepository = new SimpleAppointmentRepository(prisma);

/**
 * Create appointment endpoint
 */
async function createAppointment(appointmentData: CreateAppointmentRequest): Promise<Appointment> {
  console.log('🎯 Creating appointment with unified library...');
  console.log('📋 Input data:', JSON.stringify(appointmentData, null, 2));

  try {
    // Step 1: Validate using AppointmentModel
    console.log('✅ Step 1: Validating with AppointmentModel...');
    const validationModel = new AppointmentModel({
      ...appointmentData,
      id: 'temp_id_' + Date.now(),
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    console.log('✅ Validation passed');

    // Step 2: Create domain entity
    console.log('✅ Step 2: Creating domain entity...');
    const entityProps: IAppointmentProps = {
      createdAt: new Date(),
      updatedAt: new Date(),
      ...appointmentData,
    };
    const appointmentEntity = new AppointmentEntity(entityProps);
    console.log('✅ Domain entity created');

    // Step 3: Save to database using repository
    console.log('✅ Step 3: Saving to database...');
    const createdAppointment = await appointmentRepository.create(appointmentData);
    console.log('✅ Appointment saved to database');
    console.log('📋 Created appointment:', JSON.stringify(createdAppointment, null, 2));

    return createdAppointment;

  } catch (error) {
    console.error('❌ Error creating appointment:', error);
    throw error;
  }
}

/**
 * Get appointments endpoint
 */
async function getAppointments(salonId?: string): Promise<Appointment[]> {
  console.log('🔍 Getting appointments...');
  
  try {
    let appointments: Appointment[];
    
    if (salonId) {
      appointments = await appointmentRepository.findBySalonId(salonId);
      console.log(`✅ Found ${appointments.length} appointments for salon ${salonId}`);
    } else {
      // Get all appointments (we'll implement this)
      appointments = await appointmentRepository.findBySalonId(''); // This will need to be updated
      console.log(`✅ Found ${appointments.length} total appointments`);
    }
    
    return appointments;
  } catch (error) {
    console.error('❌ Error getting appointments:', error);
    throw error;
  }
}

/**
 * HTTP Server
 */
const server = serve({
  port: 3002,
  async fetch(req) {
    const url = new URL(req.url);
    const path = url.pathname;
    const method = req.method;

    // CORS headers
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // Handle CORS preflight
    if (method === 'OPTIONS') {
      return new Response(null, { status: 200, headers: corsHeaders });
    }

    try {
      // Health check
      if (path === '/health') {
        return new Response(JSON.stringify({ status: 'ok', service: 'appointment-creator' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Create appointment
      if (path === '/appointments' && method === 'POST') {
        const body = await req.json();
        
        // Default appointment data if not provided
        const appointmentData: CreateAppointmentRequest = {
          salonId: body.salonId || 'salon_demo_001',
          customerId: body.customerId || 'customer_demo_001',
          staffId: body.staffId || 'staff_demo_001',
          treatmentId: body.treatmentId || 'treatment_demo_001',
          customerName: body.customerName || 'Demo Customer',
          customerEmail: body.customerEmail || '<EMAIL>',
          customerPhone: body.customerPhone || '+**********',
          treatmentName: body.treatmentName || 'Demo Treatment',
          treatmentDuration: body.treatmentDuration || 60,
          treatmentPrice: body.treatmentPrice || 50.0,
          startTime: body.startTime ? new Date(body.startTime) : new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
          endTime: body.endTime ? new Date(body.endTime) : new Date(Date.now() + 25 * 60 * 60 * 1000), // Tomorrow + 1 hour
          status: body.status || 'PENDING',
          notes: body.notes || 'Created via curl using unified library',
          locale: body.locale || 'en-US',
          source: body.source || 'api',
          ...body,
        };

        const appointment = await createAppointment(appointmentData);
        
        return new Response(JSON.stringify({
          success: true,
          data: appointment,
          message: 'Appointment created successfully using unified library!'
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // Get appointments
      if (path === '/appointments' && method === 'GET') {
        const salonId = url.searchParams.get('salonId') || undefined;
        const appointments = await getAppointments(salonId);
        
        return new Response(JSON.stringify({
          success: true,
          data: appointments,
          count: appointments.length
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      // API documentation
      if (path === '/' || path === '/docs') {
        const docs = `
🎯 APPOINTMENT CREATOR API - Using Unified Library

Available endpoints:

POST /appointments
- Create a new appointment
- Body: JSON with appointment data (all fields optional, defaults provided)
- Example: curl -X POST http://localhost:3001/appointments -H "Content-Type: application/json" -d '{"customerName":"John Doe","treatmentName":"Haircut"}'

GET /appointments
- Get all appointments
- Query params: ?salonId=salon_id (optional)
- Example: curl http://localhost:3001/appointments

GET /health
- Health check
- Example: curl http://localhost:3001/health

✨ This API uses the @beauty-crm/platform-appointment-unified library!
        `;
        
        return new Response(docs, {
          headers: { ...corsHeaders, 'Content-Type': 'text/plain' },
        });
      }

      return new Response('Not Found', { 
        status: 404, 
        headers: corsHeaders 
      });

    } catch (error) {
      console.error('API Error:', error);
      return new Response(JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });
    }
  },
});

console.log('🚀 Appointment Creator API running on http://localhost:3001');
console.log('📚 Visit http://localhost:3001/docs for API documentation');
console.log('🎯 Using @beauty-crm/platform-appointment-unified library');
